// frontend/src/lib/validations/auth.ts
import { z } from 'zod';
import { VALIDATION_CONFIG } from '@/constants';

// Base validation schemas
export const emailSchema = z
  .string()
  .min(1, 'Email is required')
  .email('Please enter a valid email address')
  .max(254, 'Email is too long');

export const passwordSchema = z
  .string()
  .min(VALIDATION_CONFIG.PASSWORD.MIN_LENGTH, `Password must be at least ${VALIDATION_CONFIG.PASSWORD.MIN_LENGTH} characters`)
  .max(VALIDATION_CONFIG.PASSWORD.MAX_LENGTH, `Password must be no more than ${VALIDATION_CONFIG.PASSWORD.MAX_LENGTH} characters`)
  .refine((password) => {
    if (!VALIDATION_CONFIG.PASSWORD.REQUIRE_UPPERCASE) return true;
    return /[A-Z]/.test(password);
  }, 'Password must contain at least one uppercase letter')
  .refine((password) => {
    if (!VALIDATION_CONFIG.PASSWORD.REQUIRE_LOWERCASE) return true;
    return /[a-z]/.test(password);
  }, 'Password must contain at least one lowercase letter')
  .refine((password) => {
    if (!VALIDATION_CONFIG.PASSWORD.REQUIRE_NUMBERS) return true;
    return /\d/.test(password);
  }, 'Password must contain at least one number')
  .refine((password) => {
    if (!VALIDATION_CONFIG.PASSWORD.REQUIRE_SYMBOLS) return true;
    return /[!@#$%^&*(),.?":{}|<>]/.test(password);
  }, 'Password must contain at least one special character');

export const usernameSchema = z
  .string()
  .min(VALIDATION_CONFIG.USERNAME.MIN_LENGTH, `Username must be at least ${VALIDATION_CONFIG.USERNAME.MIN_LENGTH} characters`)
  .max(VALIDATION_CONFIG.USERNAME.MAX_LENGTH, `Username must be no more than ${VALIDATION_CONFIG.USERNAME.MAX_LENGTH} characters`)
  .regex(VALIDATION_CONFIG.USERNAME.PATTERN, 'Username can only contain letters, numbers, underscores, and hyphens')
  .refine((username) => !username.startsWith('_'), 'Username cannot start with an underscore')
  .refine((username) => !username.endsWith('_'), 'Username cannot end with an underscore');

export const nameSchema = z
  .string()
  .min(1, 'Name is required')
  .max(100, 'Name is too long')
  .regex(/^[a-zA-Z\s'-]+$/, 'Name can only contain letters, spaces, apostrophes, and hyphens');

export const phoneSchema = z
  .string()
  .regex(VALIDATION_CONFIG.PHONE.PATTERN, 'Please enter a valid phone number')
  .optional()
  .or(z.literal(''));

// Authentication schemas
export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean().optional(),
});

export const registerSchema = z.object({
  username: usernameSchema,
  email: emailSchema,
  password: passwordSchema,
  confirmPassword: z.string(),
  role: z.enum(['buyer', 'supplier'], {
    required_error: 'Please select a role',
  }),
  fullName: nameSchema.optional(),
  businessName: z.string().max(200, 'Business name is too long').optional(),
  agreeToTerms: z.boolean().refine((val) => val === true, {
    message: 'You must agree to the terms and conditions',
  }),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ['confirmPassword'],
});

export const forgotPasswordSchema = z.object({
  email: emailSchema,
});

export const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Reset token is required'),
  password: passwordSchema,
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ['confirmPassword'],
});

export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: passwordSchema,
  confirmPassword: z.string(),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ['confirmPassword'],
}).refine((data) => data.currentPassword !== data.newPassword, {
  message: "New password must be different from current password",
  path: ['newPassword'],
});

export const updateProfileSchema = z.object({
  name: nameSchema.optional(),
  bio: z.string().max(500, 'Bio is too long').optional(),
  phoneNumber: phoneSchema,
  address: z.string().max(500, 'Address is too long').optional(),
  website: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  socialLinks: z.object({
    twitter: z.string().url('Please enter a valid Twitter URL').optional().or(z.literal('')),
    linkedin: z.string().url('Please enter a valid LinkedIn URL').optional().or(z.literal('')),
    github: z.string().url('Please enter a valid GitHub URL').optional().or(z.literal('')),
  }).optional(),
});

// Type exports
export type LoginFormData = z.infer<typeof loginSchema>;
export type RegisterFormData = z.infer<typeof registerSchema>;
export type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;
export type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;
export type ChangePasswordFormData = z.infer<typeof changePasswordSchema>;
export type UpdateProfileFormData = z.infer<typeof updateProfileSchema>;
