// frontend/src/contexts/NotificationContext.tsx
'use client';

import React, { createContext, useContext, useReducer, useEffect, ReactNode, useCallback } from 'react';
import { toast } from 'sonner';
import type { Notification } from '@/types';
import { NOTIFICATION_CONFIG } from '@/constants';

// Notification Action Types
type NotificationAction =
  | { type: 'ADD_NOTIFICATION'; payload: Notification }
  | { type: 'REMOVE_NOTIFICATION'; payload: { id: string } }
  | { type: 'MARK_AS_READ'; payload: { id: string } }
  | { type: 'MARK_ALL_AS_READ' }
  | { type: 'CLEAR_ALL' }
  | { type: 'SET_NOTIFICATIONS'; payload: { notifications: Notification[] } }
  | { type: 'SET_LOADING'; payload: { loading: boolean } }
  | { type: 'SET_ERROR'; payload: { error: string | null } };

// Notification State
interface NotificationState {
  notifications: Notification[];
  unreadCount: number;
  isLoading: boolean;
  error: string | null;
  lastFetch: string | null;
}

// Notification Context Type
interface NotificationContextType extends NotificationState {
  addNotification: (notification: Omit<Notification, 'id' | 'createdAt'>) => void;
  removeNotification: (id: string) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  clearAll: () => void;
  showToast: (message: string, type?: 'success' | 'error' | 'info' | 'warning') => void;
  fetchNotifications: () => Promise<void>;
}

// Initial State
const initialState: NotificationState = {
  notifications: [],
  unreadCount: 0,
  isLoading: false,
  error: null,
  lastFetch: null,
};

// Notification Reducer
const notificationReducer = (state: NotificationState, action: NotificationAction): NotificationState => {
  switch (action.type) {
    case 'ADD_NOTIFICATION': {
      const newNotification = action.payload;
      const notifications = [newNotification, ...state.notifications]
        .slice(0, NOTIFICATION_CONFIG.MAX_NOTIFICATIONS);
      
      return {
        ...state,
        notifications,
        unreadCount: notifications.filter(n => n.status === 'unread').length,
      };
    }

    case 'REMOVE_NOTIFICATION': {
      const notifications = state.notifications.filter(n => n.id !== action.payload.id);
      return {
        ...state,
        notifications,
        unreadCount: notifications.filter(n => n.status === 'unread').length,
      };
    }

    case 'MARK_AS_READ': {
      const notifications = state.notifications.map(n =>
        n.id === action.payload.id
          ? { ...n, status: 'read' as const, readAt: new Date().toISOString() }
          : n
      );
      return {
        ...state,
        notifications,
        unreadCount: notifications.filter(n => n.status === 'unread').length,
      };
    }

    case 'MARK_ALL_AS_READ': {
      const notifications = state.notifications.map(n => ({
        ...n,
        status: 'read' as const,
        readAt: n.readAt || new Date().toISOString(),
      }));
      return {
        ...state,
        notifications,
        unreadCount: 0,
      };
    }

    case 'CLEAR_ALL':
      return {
        ...state,
        notifications: [],
        unreadCount: 0,
      };

    case 'SET_NOTIFICATIONS': {
      const notifications = action.payload.notifications;
      return {
        ...state,
        notifications,
        unreadCount: notifications.filter(n => n.status === 'unread').length,
        lastFetch: new Date().toISOString(),
      };
    }

    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload.loading,
      };

    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload.error,
        isLoading: false,
      };

    default:
      return state;
  }
};

// Create Context
const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

// Notification Provider Component
interface NotificationProviderProps {
  children: ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(notificationReducer, initialState);

  // Generate unique ID for notifications
  const generateId = () => `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  // Add notification
  const addNotification = useCallback((notification: Omit<Notification, 'id' | 'createdAt'>) => {
    const newNotification: Notification = {
      ...notification,
      id: generateId(),
      createdAt: new Date().toISOString(),
    };
    
    dispatch({ type: 'ADD_NOTIFICATION', payload: newNotification });
    
    // Auto-mark as read after delay if enabled
    if (NOTIFICATION_CONFIG.AUTO_MARK_READ_DELAY > 0) {
      setTimeout(() => {
        dispatch({ type: 'MARK_AS_READ', payload: { id: newNotification.id } });
      }, NOTIFICATION_CONFIG.AUTO_MARK_READ_DELAY);
    }
  }, []);

  // Remove notification
  const removeNotification = useCallback((id: string) => {
    dispatch({ type: 'REMOVE_NOTIFICATION', payload: { id } });
  }, []);

  // Mark as read
  const markAsRead = useCallback((id: string) => {
    dispatch({ type: 'MARK_AS_READ', payload: { id } });
  }, []);

  // Mark all as read
  const markAllAsRead = useCallback(() => {
    dispatch({ type: 'MARK_ALL_AS_READ' });
  }, []);

  // Clear all notifications
  const clearAll = useCallback(() => {
    dispatch({ type: 'CLEAR_ALL' });
  }, []);

  // Show toast notification
  const showToast = useCallback((message: string, type: 'success' | 'error' | 'info' | 'warning' = 'info') => {
    switch (type) {
      case 'success':
        toast.success(message);
        break;
      case 'error':
        toast.error(message);
        break;
      case 'warning':
        toast.warning(message);
        break;
      default:
        toast(message);
    }
  }, []);

  // Fetch notifications from API
  const fetchNotifications = useCallback(async () => {
    dispatch({ type: 'SET_LOADING', payload: { loading: true } });
    
    try {
      // TODO: Replace with actual API call
      // const response = await api.get('/notifications');
      // dispatch({ type: 'SET_NOTIFICATIONS', payload: { notifications: response.data } });
      
      // Mock data for now
      const mockNotifications: Notification[] = [
        {
          id: '1',
          userId: 'user1',
          type: 'order',
          title: 'Order Confirmed',
          message: 'Your order #12345 has been confirmed',
          status: 'unread',
          createdAt: new Date().toISOString(),
        },
        {
          id: '2',
          userId: 'user1',
          type: 'like',
          title: 'New Like',
          message: 'Someone liked your post',
          status: 'unread',
          createdAt: new Date(Date.now() - 3600000).toISOString(),
        },
      ];
      
      dispatch({ type: 'SET_NOTIFICATIONS', payload: { notifications: mockNotifications } });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: { error: 'Failed to fetch notifications' } });
    }
  }, []);

  // Auto-fetch notifications on mount
  useEffect(() => {
    fetchNotifications();
  }, [fetchNotifications]);

  // Context value
  const contextValue: NotificationContextType = {
    ...state,
    addNotification,
    removeNotification,
    markAsRead,
    markAllAsRead,
    clearAll,
    showToast,
    fetchNotifications,
  };

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
    </NotificationContext.Provider>
  );
};

// Custom hook to use notification context
export const useNotificationContext = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotificationContext must be used within a NotificationProvider');
  }
  return context;
};
