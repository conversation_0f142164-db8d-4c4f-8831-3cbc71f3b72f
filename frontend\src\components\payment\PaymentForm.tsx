// frontend/src/components/payment/PaymentForm.tsx
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { CreditCard, Lock, AlertCircle } from 'lucide-react';

interface PaymentFormData {
  cardNumber: string;
  expiryDate: string;
  cvv: string;
  cardholderName: string;
  billingAddress: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
}

interface PaymentFormProps {
  amount: number;
  currency?: string;
  onSubmit: (data: PaymentFormData) => Promise<void>;
  loading?: boolean;
}

export default function PaymentForm({ 
  amount, 
  currency = 'USD', 
  onSubmit, 
  loading = false 
}: PaymentFormProps) {
  const [processing, setProcessing] = useState(false);
  const { register, handleSubmit, formState: { errors }, setValue } = useForm<PaymentFormData>();

  const handleFormSubmit = async (data: PaymentFormData) => {
    try {
      setProcessing(true);
      await onSubmit(data);
    } catch (error) {
      console.error('Payment error:', error);
    } finally {
      setProcessing(false);
    }
  };

  const formatCardNumber = (value: string) => {
    // Remove all non-digit characters
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    // Add spaces every 4 digits
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    if (parts.length) {
      return parts.join(' ');
    } else {
      return v;
    }
  };

  const formatExpiryDate = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    if (v.length >= 2) {
      return v.substring(0, 2) + '/' + v.substring(2, 4);
    }
    return v;
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center">
          <CreditCard className="mr-2 h-5 w-5" />
          Payment Details
        </CardTitle>
        <CardDescription>
          Complete your purchase securely
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
          {/* Order Summary */}
          <div className="bg-muted p-4 rounded-lg">
            <div className="flex justify-between items-center">
              <span className="font-medium">Total Amount:</span>
              <span className="text-2xl font-bold">
                {currency === 'USD' ? '$' : currency} {amount.toFixed(2)}
              </span>
            </div>
          </div>

          <Separator />

          {/* Card Information */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="cardholderName">Cardholder Name</Label>
              <Input
                id="cardholderName"
                {...register('cardholderName', { required: 'Cardholder name is required' })}
                placeholder="John Doe"
              />
              {errors.cardholderName && (
                <p className="text-sm text-red-500 mt-1">{errors.cardholderName.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="cardNumber">Card Number</Label>
              <Input
                id="cardNumber"
                {...register('cardNumber', { 
                  required: 'Card number is required',
                  pattern: {
                    value: /^[0-9\s]{13,19}$/,
                    message: 'Invalid card number'
                  }
                })}
                placeholder="1234 5678 9012 3456"
                maxLength={19}
                onChange={(e) => {
                  const formatted = formatCardNumber(e.target.value);
                  e.target.value = formatted;
                  setValue('cardNumber', formatted);
                }}
              />
              {errors.cardNumber && (
                <p className="text-sm text-red-500 mt-1">{errors.cardNumber.message}</p>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="expiryDate">Expiry Date</Label>
                <Input
                  id="expiryDate"
                  {...register('expiryDate', { 
                    required: 'Expiry date is required',
                    pattern: {
                      value: /^(0[1-9]|1[0-2])\/([0-9]{2})$/,
                      message: 'Invalid expiry date (MM/YY)'
                    }
                  })}
                  placeholder="MM/YY"
                  maxLength={5}
                  onChange={(e) => {
                    const formatted = formatExpiryDate(e.target.value);
                    e.target.value = formatted;
                    setValue('expiryDate', formatted);
                  }}
                />
                {errors.expiryDate && (
                  <p className="text-sm text-red-500 mt-1">{errors.expiryDate.message}</p>
                )}
              </div>
              <div>
                <Label htmlFor="cvv">CVV</Label>
                <Input
                  id="cvv"
                  {...register('cvv', { 
                    required: 'CVV is required',
                    pattern: {
                      value: /^[0-9]{3,4}$/,
                      message: 'Invalid CVV'
                    }
                  })}
                  placeholder="123"
                  maxLength={4}
                  type="password"
                />
                {errors.cvv && (
                  <p className="text-sm text-red-500 mt-1">{errors.cvv.message}</p>
                )}
              </div>
            </div>
          </div>

          <Separator />

          {/* Billing Address */}
          <div className="space-y-4">
            <h3 className="font-medium">Billing Address</h3>
            
            <div>
              <Label htmlFor="street">Street Address</Label>
              <Input
                id="street"
                {...register('billingAddress.street', { required: 'Street address is required' })}
                placeholder="123 Main St"
              />
              {errors.billingAddress?.street && (
                <p className="text-sm text-red-500 mt-1">{errors.billingAddress.street.message}</p>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  {...register('billingAddress.city', { required: 'City is required' })}
                  placeholder="New York"
                />
                {errors.billingAddress?.city && (
                  <p className="text-sm text-red-500 mt-1">{errors.billingAddress.city.message}</p>
                )}
              </div>
              <div>
                <Label htmlFor="state">State</Label>
                <Input
                  id="state"
                  {...register('billingAddress.state', { required: 'State is required' })}
                  placeholder="NY"
                />
                {errors.billingAddress?.state && (
                  <p className="text-sm text-red-500 mt-1">{errors.billingAddress.state.message}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="zipCode">ZIP Code</Label>
                <Input
                  id="zipCode"
                  {...register('billingAddress.zipCode', { required: 'ZIP code is required' })}
                  placeholder="10001"
                />
                {errors.billingAddress?.zipCode && (
                  <p className="text-sm text-red-500 mt-1">{errors.billingAddress.zipCode.message}</p>
                )}
              </div>
              <div>
                <Label htmlFor="country">Country</Label>
                <Select onValueChange={(value) => setValue('billingAddress.country', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select country" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="US">United States</SelectItem>
                    <SelectItem value="CA">Canada</SelectItem>
                    <SelectItem value="GB">United Kingdom</SelectItem>
                    <SelectItem value="AU">Australia</SelectItem>
                  </SelectContent>
                </Select>
                {errors.billingAddress?.country && (
                  <p className="text-sm text-red-500 mt-1">{errors.billingAddress.country.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Security Notice */}
          <div className="flex items-center space-x-2 text-sm text-muted-foreground bg-muted p-3 rounded">
            <Lock className="h-4 w-4" />
            <span>Your payment information is encrypted and secure</span>
          </div>

          {/* Submit Button */}
          <Button 
            type="submit" 
            className="w-full" 
            disabled={processing || loading}
          >
            {processing ? (
              <>Processing...</>
            ) : (
              <>
                <Lock className="mr-2 h-4 w-4" />
                Complete Payment
              </>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
