// frontend/src/hooks/index.ts

// Export all custom hooks
export { useAuth } from './useAuth';
export { useLocalStorage } from './useLocalStorage';
export { useDebounce, useDebouncedCallback, useDebouncedSearch } from './useDebounce';
export { useApi, usePaginatedApi, useMutation, useOptimisticMutation } from './useApi';
export { useCart } from './useCart';
export { useInfiniteScroll, useScrollInfiniteLoad } from './useInfiniteScroll';

// Re-export types
export type { UseAuthReturn } from './useAuth';
