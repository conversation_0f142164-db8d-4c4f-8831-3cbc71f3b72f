// frontend/src/hooks/useCart.ts
'use client';

import { useState, useEffect, useCallback } from 'react';
import { useLocalStorage } from './useLocalStorage';
import type { Product, CartItem, Cart } from '@/types';

interface UseCartReturn {
  cart: Cart;
  items: CartItem[];
  itemCount: number;
  subtotal: number;
  total: number;
  addItem: (product: Product, quantity?: number) => void;
  removeItem: (productId: string) => void;
  updateQuantity: (productId: string, quantity: number) => void;
  clearCart: () => void;
  isInCart: (productId: string) => boolean;
  getItemQuantity: (productId: string) => number;
}

const TAX_RATE = 0.08; // 8% tax
const SHIPPING_RATE = 10; // $10 flat shipping

export function useCart(): UseCartReturn {
  const [cartItems, setCartItems] = useLocalStorage<CartItem[]>('cart-items', []);
  const [cart, setCart] = useState<Cart>({
    id: 'local-cart',
    items: [],
    subtotal: 0,
    tax: 0,
    shipping: 0,
    total: 0,
    updatedAt: new Date().toISOString(),
  });

  // Calculate cart totals
  const calculateTotals = useCallback((items: CartItem[]) => {
    const subtotal = items.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);
    const tax = subtotal * TAX_RATE;
    const shipping = items.length > 0 ? SHIPPING_RATE : 0;
    const total = subtotal + tax + shipping;

    return { subtotal, tax, shipping, total };
  }, []);

  // Update cart when items change
  useEffect(() => {
    const totals = calculateTotals(cartItems);
    setCart({
      id: 'local-cart',
      items: cartItems,
      ...totals,
      updatedAt: new Date().toISOString(),
    });
  }, [cartItems, calculateTotals]);

  const addItem = useCallback((product: Product, quantity: number = 1) => {
    setCartItems(currentItems => {
      const existingItemIndex = currentItems.findIndex(item => item.productId === product.id);
      
      if (existingItemIndex >= 0) {
        // Update existing item quantity
        const updatedItems = [...currentItems];
        updatedItems[existingItemIndex] = {
          ...updatedItems[existingItemIndex],
          quantity: updatedItems[existingItemIndex].quantity + quantity,
        };
        return updatedItems;
      } else {
        // Add new item
        const newItem: CartItem = {
          productId: product.id,
          product,
          quantity,
          addedAt: new Date().toISOString(),
        };
        return [...currentItems, newItem];
      }
    });
  }, [setCartItems]);

  const removeItem = useCallback((productId: string) => {
    setCartItems(currentItems => 
      currentItems.filter(item => item.productId !== productId)
    );
  }, [setCartItems]);

  const updateQuantity = useCallback((productId: string, quantity: number) => {
    if (quantity <= 0) {
      removeItem(productId);
      return;
    }

    setCartItems(currentItems => 
      currentItems.map(item => 
        item.productId === productId 
          ? { ...item, quantity }
          : item
      )
    );
  }, [setCartItems, removeItem]);

  const clearCart = useCallback(() => {
    setCartItems([]);
  }, [setCartItems]);

  const isInCart = useCallback((productId: string) => {
    return cartItems.some(item => item.productId === productId);
  }, [cartItems]);

  const getItemQuantity = useCallback((productId: string) => {
    const item = cartItems.find(item => item.productId === productId);
    return item ? item.quantity : 0;
  }, [cartItems]);

  return {
    cart,
    items: cartItems,
    itemCount: cartItems.reduce((sum, item) => sum + item.quantity, 0),
    subtotal: cart.subtotal,
    total: cart.total,
    addItem,
    removeItem,
    updateQuantity,
    clearCart,
    isInCart,
    getItemQuantity,
  };
}
