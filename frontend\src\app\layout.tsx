// frontend/src/app/layout.tsx
import './globals.css';
import AppLayout from '@/components/ClientLayout';

export const metadata = {
  title: "Market O'Clock - B2B2C Marketplace",
  description: 'Connect suppliers with retailers in a social marketplace',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body>
        <AppLayout>{children}</AppLayout>
      </body>
    </html>
  );
}