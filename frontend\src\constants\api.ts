// frontend/src/constants/api.ts

// Base API configuration
export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001',
  TIMEOUT: 30000, // 30 seconds
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 second
} as const;

// API Endpoints
export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    FORGOT_PASSWORD: '/auth/forgot-password',
    RESET_PASSWORD: '/auth/reset-password',
    VERIFY_EMAIL: '/auth/verify-email',
    CHANGE_PASSWORD: '/auth/change-password',
    PROFILE: '/auth/profile',
  },

  // Users
  USERS: {
    BASE: '/users',
    PROFILE: (id: string) => `/users/${id}`,
    AVATAR: (id: string) => `/users/${id}/avatar`,
    FOLLOW: (id: string) => `/users/${id}/follow`,
    UNFOLLOW: (id: string) => `/users/${id}/unfollow`,
    FOLLOWERS: (id: string) => `/users/${id}/followers`,
    FOLLOWING: (id: string) => `/users/${id}/following`,
  },

  // Products
  PRODUCTS: {
    BASE: '/products',
    DETAIL: (id: string) => `/products/${id}`,
    SEARCH: '/products/search',
    CATEGORIES: '/products/categories',
    FEATURED: '/products/featured',
    TRENDING: '/products/trending',
    BY_SELLER: (sellerId: string) => `/products/seller/${sellerId}`,
    IMAGES: (id: string) => `/products/${id}/images`,
    REVIEWS: (id: string) => `/products/${id}/reviews`,
  },

  // Orders
  ORDERS: {
    BASE: '/orders',
    DETAIL: (id: string) => `/orders/${id}`,
    CREATE: '/orders',
    CANCEL: (id: string) => `/orders/${id}/cancel`,
    TRACK: (id: string) => `/orders/${id}/track`,
    HISTORY: '/orders/history',
    BY_SELLER: '/orders/seller',
  },

  // Cart
  CART: {
    BASE: '/cart',
    ADD: '/cart/add',
    REMOVE: (itemId: string) => `/cart/remove/${itemId}`,
    UPDATE: (itemId: string) => `/cart/update/${itemId}`,
    CLEAR: '/cart/clear',
    CHECKOUT: '/cart/checkout',
  },

  // Posts & Blog
  POSTS: {
    BASE: '/posts',
    DETAIL: (id: string) => `/posts/${id}`,
    CREATE: '/posts',
    UPDATE: (id: string) => `/posts/${id}`,
    DELETE: (id: string) => `/posts/${id}`,
    LIKE: (id: string) => `/posts/${id}/like`,
    UNLIKE: (id: string) => `/posts/${id}/unlike`,
    COMMENTS: (id: string) => `/posts/${id}/comments`,
    BY_AUTHOR: (authorId: string) => `/posts/author/${authorId}`,
    BY_CATEGORY: (categoryId: string) => `/posts/category/${categoryId}`,
  },

  // Microblog
  MICROBLOG: {
    BASE: '/microblog',
    DETAIL: (id: string) => `/microblog/${id}`,
    CREATE: '/microblog',
    UPDATE: (id: string) => `/microblog/${id}`,
    DELETE: (id: string) => `/microblog/${id}`,
    LIKE: (id: string) => `/microblog/${id}/like`,
    UNLIKE: (id: string) => `/microblog/${id}/unlike`,
    FEED: '/microblog/feed',
    TRENDING: '/microblog/trending',
  },

  // Comments
  COMMENTS: {
    BASE: '/comments',
    DETAIL: (id: string) => `/comments/${id}`,
    CREATE: '/comments',
    UPDATE: (id: string) => `/comments/${id}`,
    DELETE: (id: string) => `/comments/${id}`,
    REPLIES: (id: string) => `/comments/${id}/replies`,
  },

  // Categories
  CATEGORIES: {
    BASE: '/categories',
    DETAIL: (id: string) => `/categories/${id}`,
    CREATE: '/categories',
    UPDATE: (id: string) => `/categories/${id}`,
    DELETE: (id: string) => `/categories/${id}`,
    TREE: '/categories/tree',
  },

  // Notifications
  NOTIFICATIONS: {
    BASE: '/notifications',
    MARK_READ: (id: string) => `/notifications/${id}/read`,
    MARK_ALL_READ: '/notifications/mark-all-read',
    DELETE: (id: string) => `/notifications/${id}`,
    SETTINGS: '/notifications/settings',
    UNREAD_COUNT: '/notifications/unread-count',
  },

  // Sellers
  SELLERS: {
    BASE: '/sellers',
    DETAIL: (id: string) => `/sellers/${id}`,
    CREATE: '/sellers',
    UPDATE: (id: string) => `/sellers/${id}`,
    PRODUCTS: (id: string) => `/sellers/${id}/products`,
    REVIEWS: (id: string) => `/sellers/${id}/reviews`,
    STATS: (id: string) => `/sellers/${id}/stats`,
  },

  // Search
  SEARCH: {
    GLOBAL: '/search',
    PRODUCTS: '/search/products',
    POSTS: '/search/posts',
    USERS: '/search/users',
    SUGGESTIONS: '/search/suggestions',
    AUTOCOMPLETE: '/search/autocomplete',
  },

  // Analytics
  ANALYTICS: {
    DASHBOARD: '/analytics/dashboard',
    PRODUCTS: '/analytics/products',
    ORDERS: '/analytics/orders',
    USERS: '/analytics/users',
    REVENUE: '/analytics/revenue',
    TRAFFIC: '/analytics/traffic',
  },

  // File Upload
  UPLOAD: {
    IMAGE: '/upload/image',
    FILE: '/upload/file',
    AVATAR: '/upload/avatar',
    PRODUCT_IMAGE: '/upload/product-image',
    BULK: '/upload/bulk',
  },

  // Payment
  PAYMENT: {
    PROCESS: '/payment/process',
    VERIFY: '/payment/verify',
    REFUND: '/payment/refund',
    METHODS: '/payment/methods',
    HISTORY: '/payment/history',
  },

  // Admin
  ADMIN: {
    DASHBOARD: '/admin/dashboard',
    USERS: '/admin/users',
    PRODUCTS: '/admin/products',
    ORDERS: '/admin/orders',
    REPORTS: '/admin/reports',
    SETTINGS: '/admin/settings',
  },
} as const;

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
} as const;

// API Error Codes
export const API_ERROR_CODES = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  AUTHENTICATION_FAILED: 'AUTHENTICATION_FAILED',
  AUTHORIZATION_FAILED: 'AUTHORIZATION_FAILED',
  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
  DUPLICATE_RESOURCE: 'DUPLICATE_RESOURCE',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  SERVER_ERROR: 'SERVER_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
} as const;

// Request Headers
export const REQUEST_HEADERS = {
  CONTENT_TYPE: 'Content-Type',
  AUTHORIZATION: 'Authorization',
  ACCEPT: 'Accept',
  USER_AGENT: 'User-Agent',
  X_REQUESTED_WITH: 'X-Requested-With',
  X_CSRF_TOKEN: 'X-CSRF-Token',
} as const;

// Content Types
export const CONTENT_TYPES = {
  JSON: 'application/json',
  FORM_DATA: 'multipart/form-data',
  URL_ENCODED: 'application/x-www-form-urlencoded',
  TEXT: 'text/plain',
  HTML: 'text/html',
} as const;
