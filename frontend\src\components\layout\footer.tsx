// src/components/footer.tsx
'use client';

import Link from 'next/link';

export default function Footer() {
  return (
    <footer className="bg-slate-900 border-t border-slate-800" aria-label="Site footer">
      <div className="container mx-auto py-8 px-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <section aria-labelledby="footer-company-title">
            <h3 id="footer-company-title" className="text-lg font-semibold text-white">Market O'Clock</h3>
            <p className="mt-2 text-sm text-slate-400">
              Connecting suppliers and retailers with innovative marketplace solutions.
            </p>
          </section>

          {/* Marketplace Links */}
          <nav aria-label="Marketplace" className="text-sm">
            <h3 className="text-sm font-semibold text-white">Marketplace</h3>
            <ul className="mt-2 space-y-2">
              <li>
                <Link href="/categories" className="text-slate-400 hover:text-white transition-colors">
                  Categories
                </Link>
              </li>
              <li>
                <Link href="/popular" className="text-slate-400 hover:text-white transition-colors">
                  Popular Items
                </Link>
              </li>
              <li>
                <Link href="/new" className="text-slate-400 hover:text-white transition-colors">
                  New Arrivals
                </Link>
              </li>
              <li>
                <Link href="/marketplace" className="text-slate-400 hover:text-white transition-colors">
                  Marketplace
                </Link>
              </li>
            </ul>
          </nav>

          {/* Company Links */}
          <nav aria-label="Company" className="text-sm">
            <h3 className="text-sm font-semibold text-white">Company</h3>
            <ul className="mt-2 space-y-2">
              <li>
                <Link href="/about" className="text-slate-400 hover:text-white transition-colors">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-slate-400 hover:text-white transition-colors">
                  Contact
                </Link>
              </li>
              <li>
                <Link href="/careers" className="text-slate-400 hover:text-white transition-colors">
                  Careers
                </Link>
              </li>
              <li>
                <Link href="/blogs" className="text-slate-400 hover:text-white transition-colors">
                  Blogs
                </Link>
              </li>
            </ul>
          </nav>

          {/* Legal Links */}
          <nav aria-label="Legal" className="text-sm">
            <h3 className="text-sm font-semibold text-white">Legal</h3>
            <ul className="mt-2 space-y-2">
              <li>
                <Link href="/terms" className="text-slate-400 hover:text-white transition-colors">
                  Terms of Service
                </Link>
              </li>
              <li>
                <Link href="/privacy" className="text-slate-400 hover:text-white transition-colors">
                  Privacy Policy
                </Link>
              </li>
            </ul>
          </nav>
        </div>

        {/* Contact and Copyright */}
        <div className="mt-8 pt-8 border-t border-slate-800 text-center text-sm text-slate-400">
          <address className="not-italic mb-2"><EMAIL> | Nairobi, Kenya</address>
          <p>&copy; {new Date().getFullYear()} Market O'Clock. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
}