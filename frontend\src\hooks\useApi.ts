// frontend/src/hooks/useApi.ts
'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import api from '@/utils/api';
import type { ApiResponse } from '@/types';

interface UseApiOptions {
  immediate?: boolean;
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
  retries?: number;
  retryDelay?: number;
}

interface UseApiReturn<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
  execute: (...args: any[]) => Promise<T>;
  reset: () => void;
  retry: () => void;
}

/**
 * Generic hook for API calls with loading, error, and retry functionality
 */
export function useApi<T = any>(
  apiFunction: (...args: any[]) => Promise<T>,
  options: UseApiOptions = {}
): UseApiReturn<T> {
  const {
    immediate = false,
    onSuccess,
    onError,
    retries = 0,
    retryDelay = 1000,
  } = options;

  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  
  const retryCountRef = useRef(0);
  const lastArgsRef = useRef<any[]>([]);
  const mountedRef = useRef(true);

  const execute = useCallback(
    async (...args: any[]): Promise<T> => {
      if (!mountedRef.current) return Promise.reject(new Error('Component unmounted'));

      lastArgsRef.current = args;
      setLoading(true);
      setError(null);

      try {
        const result = await apiFunction(...args);
        
        if (mountedRef.current) {
          setData(result);
          retryCountRef.current = 0;
          onSuccess?.(result);
        }
        
        return result;
      } catch (err) {
        const error = err instanceof Error ? err : new Error('An error occurred');
        
        if (mountedRef.current) {
          setError(error);
          onError?.(error);

          // Retry logic
          if (retryCountRef.current < retries) {
            retryCountRef.current++;
            setTimeout(() => {
              if (mountedRef.current) {
                execute(...args);
              }
            }, retryDelay);
          }
        }
        
        throw error;
      } finally {
        if (mountedRef.current) {
          setLoading(false);
        }
      }
    },
    [apiFunction, onSuccess, onError, retries, retryDelay]
  );

  const reset = useCallback(() => {
    setData(null);
    setError(null);
    setLoading(false);
    retryCountRef.current = 0;
  }, []);

  const retry = useCallback(() => {
    if (lastArgsRef.current.length > 0) {
      execute(...lastArgsRef.current);
    }
  }, [execute]);

  // Execute immediately if requested
  useEffect(() => {
    if (immediate) {
      execute();
    }
  }, [immediate, execute]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  return {
    data,
    loading,
    error,
    execute,
    reset,
    retry,
  };
}

/**
 * Hook for paginated API calls
 */
export function usePaginatedApi<T = any>(
  apiFunction: (page: number, limit: number, ...args: any[]) => Promise<{ items: T[]; meta: any }>,
  limit: number = 10,
  options: UseApiOptions = {}
) {
  const [page, setPage] = useState(1);
  const [allData, setAllData] = useState<T[]>([]);
  const [hasMore, setHasMore] = useState(true);
  const [meta, setMeta] = useState<any>(null);

  const { data, loading, error, execute, reset: apiReset } = useApi(
    (currentPage: number, ...args: any[]) => apiFunction(currentPage, limit, ...args),
    {
      ...options,
      onSuccess: (result) => {
        if (page === 1) {
          setAllData(result.items);
        } else {
          setAllData(prev => [...prev, ...result.items]);
        }
        setMeta(result.meta);
        setHasMore(result.items.length === limit && result.meta?.hasNext !== false);
        options.onSuccess?.(result);
      },
    }
  );

  const loadMore = useCallback(
    (...args: any[]) => {
      if (!loading && hasMore) {
        const nextPage = page + 1;
        setPage(nextPage);
        execute(nextPage, ...args);
      }
    },
    [loading, hasMore, page, execute]
  );

  const refresh = useCallback(
    (...args: any[]) => {
      setPage(1);
      setAllData([]);
      setHasMore(true);
      execute(1, ...args);
    },
    [execute]
  );

  const reset = useCallback(() => {
    setPage(1);
    setAllData([]);
    setHasMore(true);
    setMeta(null);
    apiReset();
  }, [apiReset]);

  return {
    data: allData,
    loading,
    error,
    hasMore,
    meta,
    page,
    loadMore,
    refresh,
    reset,
  };
}

/**
 * Hook for mutations (POST, PUT, DELETE operations)
 */
export function useMutation<T = any, P = any>(
  mutationFunction: (params: P) => Promise<T>,
  options: UseApiOptions = {}
) {
  const { data, loading, error, execute, reset } = useApi(mutationFunction, {
    ...options,
    immediate: false,
  });

  const mutate = useCallback(
    (params: P) => execute(params),
    [execute]
  );

  return {
    data,
    loading,
    error,
    mutate,
    reset,
  };
}

/**
 * Hook for optimistic updates
 */
export function useOptimisticMutation<T = any, P = any>(
  mutationFunction: (params: P) => Promise<T>,
  optimisticUpdate: (params: P) => T,
  options: UseApiOptions = {}
) {
  const [optimisticData, setOptimisticData] = useState<T | null>(null);
  const [isOptimistic, setIsOptimistic] = useState(false);

  const { data, loading, error, execute, reset: mutationReset } = useApi(
    mutationFunction,
    {
      ...options,
      immediate: false,
      onSuccess: (result) => {
        setOptimisticData(null);
        setIsOptimistic(false);
        options.onSuccess?.(result);
      },
      onError: (err) => {
        setOptimisticData(null);
        setIsOptimistic(false);
        options.onError?.(err);
      },
    }
  );

  const mutate = useCallback(
    async (params: P) => {
      // Apply optimistic update
      const optimistic = optimisticUpdate(params);
      setOptimisticData(optimistic);
      setIsOptimistic(true);

      try {
        return await execute(params);
      } catch (error) {
        // Revert optimistic update on error
        setOptimisticData(null);
        setIsOptimistic(false);
        throw error;
      }
    },
    [execute, optimisticUpdate]
  );

  const reset = useCallback(() => {
    setOptimisticData(null);
    setIsOptimistic(false);
    mutationReset();
  }, [mutationReset]);

  return {
    data: isOptimistic ? optimisticData : data,
    loading,
    error,
    isOptimistic,
    mutate,
    reset,
  };
}
