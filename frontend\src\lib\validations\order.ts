// frontend/src/lib/validations/order.ts
import { z } from 'zod';
import { ORDER_CONFIG } from '@/constants';

// Address validation schema
export const addressSchema = z.object({
  street: z.string().min(1, 'Street address is required').max(200, 'Street address is too long'),
  city: z.string().min(1, 'City is required').max(100, 'City name is too long'),
  state: z.string().min(1, 'State is required').max(100, 'State name is too long'),
  zipCode: z.string()
    .min(1, 'ZIP code is required')
    .max(20, 'ZIP code is too long')
    .regex(/^[0-9A-Za-z\s-]+$/, 'Please enter a valid ZIP code'),
  country: z.string().min(1, 'Country is required').max(100, 'Country name is too long'),
  isDefault: z.boolean().optional(),
});

// Order item validation schema
export const orderItemSchema = z.object({
  productId: z.string().min(1, 'Product ID is required'),
  quantity: z.number().int().min(1, 'Quantity must be at least 1').max(999, 'Quantity is too large'),
  unitPrice: z.number().min(0, 'Unit price cannot be negative'),
  selectedVariant: z.string().optional(),
});

// Payment validation schemas
export const creditCardSchema = z.object({
  cardNumber: z.string()
    .min(1, 'Card number is required')
    .regex(/^[0-9\s]{13,19}$/, 'Please enter a valid card number')
    .transform((val) => val.replace(/\s/g, '')),
  expiryDate: z.string()
    .min(1, 'Expiry date is required')
    .regex(/^(0[1-9]|1[0-2])\/([0-9]{2})$/, 'Please enter a valid expiry date (MM/YY)')
    .refine((date) => {
      const [month, year] = date.split('/');
      const expiry = new Date(2000 + parseInt(year), parseInt(month) - 1);
      return expiry > new Date();
    }, 'Card has expired'),
  cvv: z.string()
    .min(3, 'CVV must be at least 3 digits')
    .max(4, 'CVV cannot exceed 4 digits')
    .regex(/^[0-9]+$/, 'CVV must contain only numbers'),
  cardholderName: z.string()
    .min(1, 'Cardholder name is required')
    .max(100, 'Cardholder name is too long')
    .regex(/^[a-zA-Z\s'-]+$/, 'Please enter a valid cardholder name'),
});

export const paymentMethodSchema = z.object({
  method: z.enum(ORDER_CONFIG.PAYMENT_METHODS, {
    required_error: 'Please select a payment method',
  }),
  creditCard: creditCardSchema.optional(),
  paypalEmail: z.string().email('Please enter a valid PayPal email').optional(),
  bankAccount: z.object({
    accountNumber: z.string().min(1, 'Account number is required'),
    routingNumber: z.string().min(1, 'Routing number is required'),
    accountType: z.enum(['checking', 'savings']),
  }).optional(),
}).refine((data) => {
  if (data.method === 'credit_card' || data.method === 'debit_card') {
    return !!data.creditCard;
  }
  if (data.method === 'paypal') {
    return !!data.paypalEmail;
  }
  if (data.method === 'bank_transfer') {
    return !!data.bankAccount;
  }
  return true;
}, {
  message: 'Payment details are required for the selected method',
  path: ['method'],
});

// Order creation schema
export const createOrderSchema = z.object({
  items: z.array(orderItemSchema).min(1, 'At least one item is required'),
  shippingAddress: addressSchema,
  billingAddress: addressSchema.optional(),
  useSameAddress: z.boolean().default(true),
  shippingMethod: z.enum(ORDER_CONFIG.SHIPPING_METHODS, {
    required_error: 'Please select a shipping method',
  }),
  paymentMethod: paymentMethodSchema,
  notes: z.string().max(500, 'Notes are too long').optional(),
  couponCode: z.string().max(50, 'Coupon code is too long').optional(),
}).refine((data) => {
  if (!data.useSameAddress) {
    return !!data.billingAddress;
  }
  return true;
}, {
  message: 'Billing address is required when different from shipping address',
  path: ['billingAddress'],
});

// Order update schema
export const updateOrderSchema = z.object({
  id: z.string().min(1, 'Order ID is required'),
  status: z.enum(ORDER_CONFIG.STATUSES).optional(),
  trackingNumber: z.string().max(100, 'Tracking number is too long').optional(),
  notes: z.string().max(500, 'Notes are too long').optional(),
  shippingAddress: addressSchema.optional(),
});

// Order filter schema
export const orderFilterSchema = z.object({
  status: z.enum(ORDER_CONFIG.STATUSES).optional(),
  dateFrom: z.string().datetime().optional(),
  dateTo: z.string().datetime().optional(),
  minAmount: z.number().min(0).optional(),
  maxAmount: z.number().min(0).optional(),
  customerId: z.string().optional(),
  sellerId: z.string().optional(),
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(10),
  sortBy: z.enum(['date', 'amount', 'status']).default('date'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

// Refund schema
export const createRefundSchema = z.object({
  orderId: z.string().min(1, 'Order ID is required'),
  amount: z.number().min(0.01, 'Refund amount must be greater than 0'),
  reason: z.enum([
    'customer_request',
    'defective_product',
    'wrong_item',
    'not_as_described',
    'damaged_in_shipping',
    'other',
  ], {
    required_error: 'Please select a refund reason',
  }),
  description: z.string().max(500, 'Description is too long').optional(),
  refundShipping: z.boolean().default(false),
});

// Shipping schema
export const updateShippingSchema = z.object({
  orderId: z.string().min(1, 'Order ID is required'),
  trackingNumber: z.string().min(1, 'Tracking number is required').max(100, 'Tracking number is too long'),
  carrier: z.string().min(1, 'Carrier is required').max(100, 'Carrier name is too long'),
  estimatedDelivery: z.string().datetime().optional(),
  shippingNotes: z.string().max(500, 'Shipping notes are too long').optional(),
});

// Cart validation schema
export const addToCartSchema = z.object({
  productId: z.string().min(1, 'Product ID is required'),
  quantity: z.number().int().min(1, 'Quantity must be at least 1').max(99, 'Quantity cannot exceed 99'),
  selectedVariant: z.string().optional(),
});

export const updateCartItemSchema = z.object({
  productId: z.string().min(1, 'Product ID is required'),
  quantity: z.number().int().min(0, 'Quantity cannot be negative').max(99, 'Quantity cannot exceed 99'),
});

// Coupon validation schema
export const applyCouponSchema = z.object({
  code: z.string().min(1, 'Coupon code is required').max(50, 'Coupon code is too long'),
  orderTotal: z.number().min(0, 'Order total cannot be negative'),
});

// Type exports
export type AddressData = z.infer<typeof addressSchema>;
export type OrderItemData = z.infer<typeof orderItemSchema>;
export type CreditCardData = z.infer<typeof creditCardSchema>;
export type PaymentMethodData = z.infer<typeof paymentMethodSchema>;
export type CreateOrderData = z.infer<typeof createOrderSchema>;
export type UpdateOrderData = z.infer<typeof updateOrderSchema>;
export type OrderFilterData = z.infer<typeof orderFilterSchema>;
export type CreateRefundData = z.infer<typeof createRefundSchema>;
export type UpdateShippingData = z.infer<typeof updateShippingSchema>;
export type AddToCartData = z.infer<typeof addToCartSchema>;
export type UpdateCartItemData = z.infer<typeof updateCartItemSchema>;
export type ApplyCouponData = z.infer<typeof applyCouponSchema>;
