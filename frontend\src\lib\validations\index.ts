// frontend/src/lib/validations/index.ts

// Export all validation schemas
export * from './auth';
export * from './product';
export * from './order';

// Export commonly used schemas
export {
  loginSchema,
  registerSchema,
  updateProfileSchema,
  type LoginFormData,
  type RegisterFormData,
  type UpdateProfileFormData,
} from './auth';

export {
  createProductSchema,
  updateProductSchema,
  productFilterSchema,
  type CreateProductFormData,
  type UpdateProductFormData,
  type ProductFilterData,
} from './product';

export {
  createOrderSchema,
  updateOrderSchema,
  orderFilterSchema,
  addressSchema,
  type CreateOrderData,
  type UpdateOrderData,
  type OrderFilterData,
  type AddressData,
} from './order';

// Validation utilities
import { z } from 'zod';

/**
 * Utility function to validate data against a schema
 * @param schema - Zod schema to validate against
 * @param data - Data to validate
 * @returns Validation result with success/error and data/errors
 */
export function validateData<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): { success: true; data: T } | { success: false; errors: z.ZodError } {
  try {
    const validatedData = schema.parse(data);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, errors: error };
    }
    throw error;
  }
}

/**
 * Utility function to safely validate data and return null on error
 * @param schema - Zod schema to validate against
 * @param data - Data to validate
 * @returns Validated data or null if validation fails
 */
export function safeValidate<T>(schema: z.ZodSchema<T>, data: unknown): T | null {
  try {
    return schema.parse(data);
  } catch {
    return null;
  }
}

/**
 * Utility function to get formatted error messages from Zod errors
 * @param error - Zod error object
 * @returns Object with field names as keys and error messages as values
 */
export function getErrorMessages(error: z.ZodError): Record<string, string> {
  const errors: Record<string, string> = {};
  
  error.errors.forEach((err) => {
    const path = err.path.join('.');
    errors[path] = err.message;
  });
  
  return errors;
}

/**
 * Utility function to get the first error message from a Zod error
 * @param error - Zod error object
 * @returns First error message or generic message
 */
export function getFirstErrorMessage(error: z.ZodError): string {
  return error.errors[0]?.message || 'Validation failed';
}

/**
 * Utility function to create a validation middleware for forms
 * @param schema - Zod schema to validate against
 * @returns Validation function that can be used with form libraries
 */
export function createValidator<T>(schema: z.ZodSchema<T>) {
  return (data: unknown) => {
    const result = validateData(schema, data);
    if (result.success) {
      return { data: result.data, errors: {} };
    } else {
      return { data: null, errors: getErrorMessages(result.errors) };
    }
  };
}

/**
 * Utility function to validate partial data (useful for form field validation)
 * @param schema - Zod schema to validate against
 * @param data - Partial data to validate
 * @returns Validation result
 */
export function validatePartial<T>(
  schema: z.ZodSchema<T>,
  data: Partial<T>
): { success: true; data: Partial<T> } | { success: false; errors: z.ZodError } {
  try {
    const partialSchema = schema.partial();
    const validatedData = partialSchema.parse(data);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, errors: error };
    }
    throw error;
  }
}

/**
 * Utility function to validate a single field
 * @param schema - Zod schema to validate against
 * @param fieldName - Name of the field to validate
 * @param value - Value to validate
 * @returns Validation result for the field
 */
export function validateField<T>(
  schema: z.ZodSchema<T>,
  fieldName: keyof T,
  value: unknown
): { success: true; data: unknown } | { success: false; error: string } {
  try {
    // Create a partial schema with only the specified field
    const fieldSchema = schema.shape?.[fieldName as string];
    if (!fieldSchema) {
      return { success: false, error: 'Field not found in schema' };
    }
    
    const validatedValue = fieldSchema.parse(value);
    return { success: true, data: validatedValue };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, error: getFirstErrorMessage(error) };
    }
    return { success: false, error: 'Validation failed' };
  }
}

// Common validation patterns
export const commonPatterns = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^\+?[\d\s\-\(\)]+$/,
  url: /^https?:\/\/.+/,
  slug: /^[a-z0-9-]+$/,
  username: /^[a-zA-Z0-9_-]+$/,
  password: {
    uppercase: /[A-Z]/,
    lowercase: /[a-z]/,
    number: /\d/,
    special: /[!@#$%^&*(),.?":{}|<>]/,
  },
} as const;

// Common validation messages
export const validationMessages = {
  required: 'This field is required',
  email: 'Please enter a valid email address',
  phone: 'Please enter a valid phone number',
  url: 'Please enter a valid URL',
  minLength: (min: number) => `Must be at least ${min} characters`,
  maxLength: (max: number) => `Must be no more than ${max} characters`,
  min: (min: number) => `Must be at least ${min}`,
  max: (max: number) => `Must be no more than ${max}`,
  passwordMatch: "Passwords don't match",
  invalidFormat: 'Invalid format',
  tooLong: 'This value is too long',
  tooShort: 'This value is too short',
} as const;
