// frontend/src/types/components.ts

import { ReactNode, ButtonHTMLAttributes, InputHTMLAttributes } from 'react';
import { Product, User, Order, Post, MicroblogPost, Category, Notification } from './index';

// ============================================================================
// COMMON COMPONENT PROPS
// ============================================================================

export interface BaseComponentProps {
  className?: string;
  children?: ReactNode;
  id?: string;
}

export interface LoadingProps extends BaseComponentProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  fullScreen?: boolean;
}

export interface ErrorProps extends BaseComponentProps {
  error: Error | string;
  retry?: () => void;
  showDetails?: boolean;
}

export interface EmptyStateProps extends BaseComponentProps {
  title: string;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  icon?: ReactNode;
}

// ============================================================================
// FORM COMPONENT PROPS
// ============================================================================

export interface FormFieldProps extends BaseComponentProps {
  label: string;
  name: string;
  required?: boolean;
  error?: string;
  helpText?: string;
}

export interface InputProps extends FormFieldProps, Omit<InputHTMLAttributes<HTMLInputElement>, 'name'> {
  variant?: 'default' | 'outline' | 'ghost';
}

export interface TextareaProps extends FormFieldProps {
  rows?: number;
  maxLength?: number;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
}

export interface SelectProps extends FormFieldProps {
  options: Array<{
    value: string;
    label: string;
    disabled?: boolean;
  }>;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  multiple?: boolean;
}

export interface FileUploadProps extends FormFieldProps {
  accept?: string;
  multiple?: boolean;
  maxSize?: number; // in bytes
  onUpload?: (files: File[]) => void;
  preview?: boolean;
}

// ============================================================================
// BUTTON COMPONENT PROPS
// ============================================================================

export interface ButtonProps extends BaseComponentProps, ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  loading?: boolean;
  icon?: ReactNode;
  iconPosition?: 'left' | 'right';
}

// ============================================================================
// LAYOUT COMPONENT PROPS
// ============================================================================

export interface HeaderProps extends BaseComponentProps {
  user?: User | null;
  onLogout?: () => void;
  showSearch?: boolean;
  showNotifications?: boolean;
}

export interface SidebarProps extends BaseComponentProps {
  isOpen?: boolean;
  onClose?: () => void;
  user?: User | null;
  currentPath?: string;
}

export interface FooterProps extends BaseComponentProps {
  showNewsletter?: boolean;
  showSocial?: boolean;
}

export interface LayoutProps extends BaseComponentProps {
  header?: ReactNode;
  sidebar?: ReactNode;
  footer?: ReactNode;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
}

// ============================================================================
// PRODUCT COMPONENT PROPS
// ============================================================================

export interface ProductCardProps extends BaseComponentProps {
  product: Product;
  onAddToCart?: (productId: string) => void;
  onLike?: (productId: string) => void;
  showActions?: boolean;
  variant?: 'default' | 'compact' | 'detailed';
}

export interface ProductListProps extends BaseComponentProps {
  products: Product[];
  loading?: boolean;
  error?: string;
  onLoadMore?: () => void;
  hasMore?: boolean;
  variant?: 'grid' | 'list';
  columns?: 2 | 3 | 4;
}

export interface ProductFormProps extends BaseComponentProps {
  product?: Product;
  categories: Category[];
  onSubmit: (data: any) => void;
  loading?: boolean;
  mode?: 'create' | 'edit';
}

export interface ProductFiltersProps extends BaseComponentProps {
  categories: Category[];
  filters: {
    category?: string;
    priceMin?: number;
    priceMax?: number;
    search?: string;
  };
  onFiltersChange: (filters: any) => void;
}

// ============================================================================
// USER COMPONENT PROPS
// ============================================================================

export interface UserAvatarProps extends BaseComponentProps {
  user: User;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  showName?: boolean;
  showStatus?: boolean;
  clickable?: boolean;
  onClick?: () => void;
}

export interface UserProfileProps extends BaseComponentProps {
  user: User;
  isOwnProfile?: boolean;
  onFollow?: () => void;
  onMessage?: () => void;
  onEdit?: () => void;
}

export interface UserListProps extends BaseComponentProps {
  users: User[];
  loading?: boolean;
  onUserClick?: (user: User) => void;
  showActions?: boolean;
}

// ============================================================================
// ORDER COMPONENT PROPS
// ============================================================================

export interface OrderCardProps extends BaseComponentProps {
  order: Order;
  onViewDetails?: (orderId: string) => void;
  onTrackOrder?: (orderId: string) => void;
  showActions?: boolean;
}

export interface OrderListProps extends BaseComponentProps {
  orders: Order[];
  loading?: boolean;
  onOrderClick?: (order: Order) => void;
  variant?: 'customer' | 'seller' | 'admin';
}

export interface OrderStatusProps extends BaseComponentProps {
  status: Order['status'];
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
}

// ============================================================================
// POST COMPONENT PROPS
// ============================================================================

export interface PostCardProps extends BaseComponentProps {
  post: Post;
  onLike?: (postId: string) => void;
  onComment?: (postId: string) => void;
  onShare?: (postId: string) => void;
  showActions?: boolean;
  variant?: 'default' | 'compact' | 'detailed';
}

export interface PostListProps extends BaseComponentProps {
  posts: Post[];
  loading?: boolean;
  onLoadMore?: () => void;
  hasMore?: boolean;
}

export interface PostFormProps extends BaseComponentProps {
  post?: Post;
  categories: Category[];
  onSubmit: (data: any) => void;
  loading?: boolean;
  mode?: 'create' | 'edit';
}

// ============================================================================
// MICROBLOG COMPONENT PROPS
// ============================================================================

export interface MicroblogPostProps extends BaseComponentProps {
  post: MicroblogPost;
  onLike?: (postId: string) => void;
  onComment?: (postId: string) => void;
  onDelete?: (postId: string) => void;
  showActions?: boolean;
}

export interface MicroblogFeedProps extends BaseComponentProps {
  posts: MicroblogPost[];
  loading?: boolean;
  onLoadMore?: () => void;
  hasMore?: boolean;
}

export interface CreateMicroblogPostProps extends BaseComponentProps {
  onSubmit: (content: string, image?: File) => void;
  loading?: boolean;
  placeholder?: string;
}

// ============================================================================
// NOTIFICATION COMPONENT PROPS
// ============================================================================

export interface NotificationItemProps extends BaseComponentProps {
  notification: Notification;
  onMarkAsRead?: (notificationId: string) => void;
  onDelete?: (notificationId: string) => void;
  onClick?: (notification: Notification) => void;
}

export interface NotificationListProps extends BaseComponentProps {
  notifications: Notification[];
  loading?: boolean;
  onMarkAllAsRead?: () => void;
  onLoadMore?: () => void;
  hasMore?: boolean;
}

export interface NotificationBellProps extends BaseComponentProps {
  unreadCount: number;
  onClick?: () => void;
  maxCount?: number;
}

// ============================================================================
// SEARCH COMPONENT PROPS
// ============================================================================

export interface SearchBarProps extends BaseComponentProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  onSearch?: (query: string) => void;
  suggestions?: string[];
  loading?: boolean;
  showFilters?: boolean;
}

export interface SearchResultsProps extends BaseComponentProps {
  query: string;
  results: {
    products?: Product[];
    posts?: Post[];
    users?: User[];
  };
  loading?: boolean;
  onLoadMore?: () => void;
  hasMore?: boolean;
}

// ============================================================================
// CART COMPONENT PROPS
// ============================================================================

export interface CartItemProps extends BaseComponentProps {
  item: {
    product: Product;
    quantity: number;
  };
  onUpdateQuantity?: (productId: string, quantity: number) => void;
  onRemove?: (productId: string) => void;
  readonly?: boolean;
}

export interface CartSummaryProps extends BaseComponentProps {
  items: Array<{
    product: Product;
    quantity: number;
  }>;
  subtotal: number;
  tax: number;
  shipping: number;
  total: number;
  onCheckout?: () => void;
  loading?: boolean;
}

// ============================================================================
// MODAL & DIALOG COMPONENT PROPS
// ============================================================================

export interface ModalProps extends BaseComponentProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  closeOnOverlayClick?: boolean;
  showCloseButton?: boolean;
}

export interface ConfirmDialogProps extends BaseComponentProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  variant?: 'default' | 'destructive';
}

// ============================================================================
// TABLE COMPONENT PROPS
// ============================================================================

export interface TableColumn<T = any> {
  key: string;
  title: string;
  render?: (value: any, record: T) => ReactNode;
  sortable?: boolean;
  width?: string | number;
  align?: 'left' | 'center' | 'right';
}

export interface TableProps<T = any> extends BaseComponentProps {
  data: T[];
  columns: TableColumn<T>[];
  loading?: boolean;
  pagination?: {
    current: number;
    total: number;
    pageSize: number;
    onChange: (page: number) => void;
  };
  rowKey?: string | ((record: T) => string);
  onRowClick?: (record: T) => void;
  selectable?: boolean;
  selectedRows?: string[];
  onSelectionChange?: (selectedRows: string[]) => void;
}

// ============================================================================
// DASHBOARD COMPONENT PROPS
// ============================================================================

export interface DashboardCardProps extends BaseComponentProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    type: 'increase' | 'decrease';
  };
  icon?: ReactNode;
  loading?: boolean;
}

export interface ChartProps extends BaseComponentProps {
  data: Array<{
    label: string;
    value: number;
  }>;
  type?: 'line' | 'bar' | 'pie' | 'area';
  height?: number;
  loading?: boolean;
}
