// frontend/src/types/api.ts

import {
  User,
  Product,
  Order,
  Post,
  MicroblogPost,
  Comment,
  Category,
  Notification,
  Seller,
  PaginatedResponse,
  ApiResponse,
  AuthResponse,
  LoginCredentials,
  RegisterData,
  DashboardStats,
  SearchFilters,
  SearchResult,
} from './index';

// ============================================================================
// AUTH API TYPES
// ============================================================================

export interface LoginRequest extends LoginCredentials {}

export interface RegisterRequest extends RegisterData {}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  token: string;
  newPassword: string;
}

// ============================================================================
// USER API TYPES
// ============================================================================

export interface UpdateUserRequest {
  name?: string;
  bio?: string;
  phoneNumber?: string;
  address?: string;
  avatarUrl?: string;
}

export interface UserProfileResponse extends User {
  followersCount: number;
  followingCount: number;
  postsCount: number;
  productsCount: number;
}

// ============================================================================
// PRODUCT API TYPES
// ============================================================================

export interface CreateProductRequest {
  name: string;
  description: string;
  price: number;
  stock: number;
  categoryId: string;
  images?: string[];
  tags?: string[];
  specifications?: Record<string, any>;
}

export interface UpdateProductRequest extends Partial<CreateProductRequest> {
  status?: 'active' | 'inactive' | 'draft';
}

export interface ProductsQuery {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  sellerId?: string;
  priceMin?: number;
  priceMax?: number;
  sortBy?: 'price' | 'date' | 'popularity';
  sortOrder?: 'asc' | 'desc';
  status?: 'active' | 'inactive' | 'all';
}

export type ProductsResponse = PaginatedResponse<Product>;
export type ProductResponse = ApiResponse<Product>;
export type ProductSearchResponse = SearchResult<Product>;

// ============================================================================
// ORDER API TYPES
// ============================================================================

export interface CreateOrderRequest {
  items: Array<{
    productId: string;
    quantity: number;
  }>;
  shippingAddress: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  billingAddress?: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  paymentMethod: 'credit_card' | 'paypal' | 'bank_transfer';
  notes?: string;
}

export interface UpdateOrderRequest {
  status?: 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  trackingNumber?: string;
  notes?: string;
}

export interface OrdersQuery {
  page?: number;
  limit?: number;
  status?: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  userId?: string;
  sellerId?: string;
  dateFrom?: string;
  dateTo?: string;
}

export type OrdersResponse = PaginatedResponse<Order>;
export type OrderResponse = ApiResponse<Order>;

// ============================================================================
// POST API TYPES
// ============================================================================

export interface CreatePostRequest {
  title: string;
  content: string;
  categoryId?: string;
  tags?: string[];
  imageUrl?: string;
  published?: boolean;
}

export interface UpdatePostRequest extends Partial<CreatePostRequest> {}

export interface PostsQuery {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  authorId?: string;
  published?: boolean;
  sortBy?: 'date' | 'popularity' | 'title';
  sortOrder?: 'asc' | 'desc';
}

export type PostsResponse = PaginatedResponse<Post>;
export type PostResponse = ApiResponse<Post>;

// ============================================================================
// MICROBLOG API TYPES
// ============================================================================

export interface CreateMicroblogPostRequest {
  content: string;
  imageUrl?: string;
}

export interface UpdateMicroblogPostRequest {
  content?: string;
  imageUrl?: string;
}

export interface MicroblogQuery {
  page?: number;
  limit?: number;
  authorId?: string;
  following?: boolean; // Get posts from followed users
}

export type MicroblogResponse = PaginatedResponse<MicroblogPost>;
export type MicroblogPostResponse = ApiResponse<MicroblogPost>;

// ============================================================================
// COMMENT API TYPES
// ============================================================================

export interface CreateCommentRequest {
  postId: string;
  content: string;
  parentId?: string; // For nested comments
}

export interface UpdateCommentRequest {
  content: string;
}

export interface CommentsQuery {
  postId: string;
  page?: number;
  limit?: number;
  sortBy?: 'date' | 'popularity';
  sortOrder?: 'asc' | 'desc';
}

export type CommentsResponse = PaginatedResponse<Comment>;
export type CommentResponse = ApiResponse<Comment>;

// ============================================================================
// CATEGORY API TYPES
// ============================================================================

export interface CreateCategoryRequest {
  name: string;
  description?: string;
  parentId?: string;
  imageUrl?: string;
}

export interface UpdateCategoryRequest extends Partial<CreateCategoryRequest> {}

export type CategoriesResponse = ApiResponse<Category[]>;
export type CategoryResponse = ApiResponse<Category>;

// ============================================================================
// NOTIFICATION API TYPES
// ============================================================================

export interface NotificationsQuery {
  page?: number;
  limit?: number;
  status?: 'unread' | 'read' | 'all';
  type?: 'order' | 'message' | 'like' | 'comment' | 'follow' | 'system';
}

export interface MarkNotificationRequest {
  status: 'read' | 'archived';
}

export type NotificationsResponse = PaginatedResponse<Notification>;
export type NotificationResponse = ApiResponse<Notification>;

// ============================================================================
// SELLER API TYPES
// ============================================================================

export interface CreateSellerRequest {
  name: string;
  businessName?: string;
  description?: string;
  logoUrl?: string;
}

export interface UpdateSellerRequest extends Partial<CreateSellerRequest> {}

export interface SellersQuery {
  page?: number;
  limit?: number;
  search?: string;
  verified?: boolean;
  sortBy?: 'rating' | 'date' | 'name';
  sortOrder?: 'asc' | 'desc';
}

export type SellersResponse = PaginatedResponse<Seller>;
export type SellerResponse = ApiResponse<Seller>;

// ============================================================================
// DASHBOARD API TYPES
// ============================================================================

export interface DashboardQuery {
  period?: 'day' | 'week' | 'month' | 'year';
  dateFrom?: string;
  dateTo?: string;
}

export type DashboardResponse = ApiResponse<DashboardStats>;

// ============================================================================
// SEARCH API TYPES
// ============================================================================

export interface GlobalSearchRequest {
  query: string;
  type?: 'products' | 'posts' | 'users' | 'all';
  filters?: SearchFilters;
  page?: number;
  limit?: number;
}

export interface GlobalSearchResponse {
  products?: SearchResult<Product>;
  posts?: SearchResult<Post>;
  users?: SearchResult<User>;
  total: number;
}

// ============================================================================
// FILE UPLOAD API TYPES
// ============================================================================

export interface FileUploadRequest {
  file: File;
  type: 'avatar' | 'product' | 'post' | 'category';
  resize?: {
    width: number;
    height: number;
  };
}

export interface FileUploadResponse {
  url: string;
  filename: string;
  size: number;
  mimeType: string;
}

// ============================================================================
// ANALYTICS API TYPES
// ============================================================================

export interface AnalyticsQuery {
  metric: 'views' | 'clicks' | 'conversions' | 'revenue';
  period: 'day' | 'week' | 'month' | 'year';
  dateFrom: string;
  dateTo: string;
  productId?: string;
  categoryId?: string;
}

export interface AnalyticsResponse {
  data: Array<{
    date: string;
    value: number;
  }>;
  total: number;
  change: number; // Percentage change from previous period
}

// ============================================================================
// WEBHOOK TYPES
// ============================================================================

export interface WebhookEvent {
  id: string;
  type: string;
  data: Record<string, any>;
  timestamp: string;
  signature: string;
}
