// frontend/src/contexts/index.tsx
'use client';

import React, { ReactNode } from 'react';
import { ThemeProvider } from './ThemeContext';
import { CartProvider } from './CartContext';
import { NotificationProvider } from './NotificationContext';

// Export individual contexts
export { ThemeProvider, useTheme, ThemeScript } from './ThemeContext';
export { CartProvider, useCartContext } from './CartContext';
export { NotificationProvider, useNotificationContext } from './NotificationContext';

// Combined App Providers Component
interface AppProvidersProps {
  children: ReactNode;
}

export const AppProviders: React.FC<AppProvidersProps> = ({ children }) => {
  return (
    <ThemeProvider defaultTheme="system" enableSystem>
      <NotificationProvider>
        <CartProvider>
          {children}
        </CartProvider>
      </NotificationProvider>
    </ThemeProvider>
  );
};
