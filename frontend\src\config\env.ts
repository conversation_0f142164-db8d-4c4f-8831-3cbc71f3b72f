// frontend/src/config/env.ts

/**
 * Environment configuration with validation and type safety
 */

// Environment variable validation schema
interface EnvConfig {
  // App Configuration
  NODE_ENV: 'development' | 'production' | 'test';
  NEXT_PUBLIC_APP_URL: string;
  NEXT_PUBLIC_APP_NAME: string;
  NEXT_PUBLIC_APP_VERSION: string;

  // API Configuration
  NEXT_PUBLIC_API_URL: string;
  NEXT_PUBLIC_API_TIMEOUT: number;

  // Authentication
  NEXT_PUBLIC_AUTH_ENABLED: boolean;
  NEXTAUTH_SECRET?: string;
  NEXTAUTH_URL?: string;

  // Third-party Services
  NEXT_PUBLIC_GOOGLE_ANALYTICS_ID?: string;
  NEXT_PUBLIC_SENTRY_DSN?: string;
  NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY?: string;

  // Feature Flags
  NEXT_PUBLIC_ENABLE_ANALYTICS: boolean;
  NEXT_PUBLIC_ENABLE_PWA: boolean;
  NEXT_PUBLIC_ENABLE_NOTIFICATIONS: boolean;
  NEXT_PUBLIC_ENABLE_DARK_MODE: boolean;

  // Development
  NEXT_PUBLIC_DEBUG: boolean;
  NEXT_PUBLIC_MOCK_API: boolean;
}

// Helper function to parse boolean environment variables
const parseBoolean = (value: string | undefined, defaultValue: boolean = false): boolean => {
  if (!value) return defaultValue;
  return value.toLowerCase() === 'true' || value === '1';
};

// Helper function to parse number environment variables
const parseNumber = (value: string | undefined, defaultValue: number): number => {
  if (!value) return defaultValue;
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? defaultValue : parsed;
};

// Validate and parse environment variables
const createEnvConfig = (): EnvConfig => {
  const config: EnvConfig = {
    // App Configuration
    NODE_ENV: (process.env.NODE_ENV as EnvConfig['NODE_ENV']) || 'development',
    NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
    NEXT_PUBLIC_APP_NAME: process.env.NEXT_PUBLIC_APP_NAME || 'Market O\'Clock',
    NEXT_PUBLIC_APP_VERSION: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',

    // API Configuration
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001',
    NEXT_PUBLIC_API_TIMEOUT: parseNumber(process.env.NEXT_PUBLIC_API_TIMEOUT, 30000),

    // Authentication
    NEXT_PUBLIC_AUTH_ENABLED: parseBoolean(process.env.NEXT_PUBLIC_AUTH_ENABLED, true),
    NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
    NEXTAUTH_URL: process.env.NEXTAUTH_URL,

    // Third-party Services
    NEXT_PUBLIC_GOOGLE_ANALYTICS_ID: process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID,
    NEXT_PUBLIC_SENTRY_DSN: process.env.NEXT_PUBLIC_SENTRY_DSN,
    NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,

    // Feature Flags
    NEXT_PUBLIC_ENABLE_ANALYTICS: parseBoolean(process.env.NEXT_PUBLIC_ENABLE_ANALYTICS, true),
    NEXT_PUBLIC_ENABLE_PWA: parseBoolean(process.env.NEXT_PUBLIC_ENABLE_PWA, true),
    NEXT_PUBLIC_ENABLE_NOTIFICATIONS: parseBoolean(process.env.NEXT_PUBLIC_ENABLE_NOTIFICATIONS, true),
    NEXT_PUBLIC_ENABLE_DARK_MODE: parseBoolean(process.env.NEXT_PUBLIC_ENABLE_DARK_MODE, true),

    // Development
    NEXT_PUBLIC_DEBUG: parseBoolean(process.env.NEXT_PUBLIC_DEBUG, config.NODE_ENV === 'development'),
    NEXT_PUBLIC_MOCK_API: parseBoolean(process.env.NEXT_PUBLIC_MOCK_API, false),
  };

  // Validation
  if (config.NODE_ENV === 'production') {
    // Required in production
    if (!config.NEXT_PUBLIC_APP_URL.startsWith('https://')) {
      console.warn('Warning: APP_URL should use HTTPS in production');
    }
    
    if (!config.NEXTAUTH_SECRET) {
      console.warn('Warning: NEXTAUTH_SECRET is not set in production');
    }
  }

  return config;
};

// Export the configuration
export const env = createEnvConfig();

// Export individual config sections for convenience
export const appConfig = {
  name: env.NEXT_PUBLIC_APP_NAME,
  version: env.NEXT_PUBLIC_APP_VERSION,
  url: env.NEXT_PUBLIC_APP_URL,
  env: env.NODE_ENV,
  debug: env.NEXT_PUBLIC_DEBUG,
} as const;

export const apiConfig = {
  baseUrl: env.NEXT_PUBLIC_API_URL,
  timeout: env.NEXT_PUBLIC_API_TIMEOUT,
  mockEnabled: env.NEXT_PUBLIC_MOCK_API,
} as const;

export const authConfig = {
  enabled: env.NEXT_PUBLIC_AUTH_ENABLED,
  secret: env.NEXTAUTH_SECRET,
  url: env.NEXTAUTH_URL,
} as const;

export const featureFlags = {
  analytics: env.NEXT_PUBLIC_ENABLE_ANALYTICS,
  pwa: env.NEXT_PUBLIC_ENABLE_PWA,
  notifications: env.NEXT_PUBLIC_ENABLE_NOTIFICATIONS,
  darkMode: env.NEXT_PUBLIC_ENABLE_DARK_MODE,
} as const;

export const thirdPartyConfig = {
  googleAnalytics: env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID,
  sentry: env.NEXT_PUBLIC_SENTRY_DSN,
  stripe: env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
} as const;

// Utility functions
export const isDevelopment = () => env.NODE_ENV === 'development';
export const isProduction = () => env.NODE_ENV === 'production';
export const isTest = () => env.NODE_ENV === 'test';

// Debug logging in development
if (isDevelopment() && env.NEXT_PUBLIC_DEBUG) {
  console.log('Environment Configuration:', {
    app: appConfig,
    api: apiConfig,
    auth: { ...authConfig, secret: authConfig.secret ? '[HIDDEN]' : undefined },
    features: featureFlags,
    thirdParty: {
      ...thirdPartyConfig,
      stripe: thirdPartyConfig.stripe ? '[HIDDEN]' : undefined,
    },
  });
}
