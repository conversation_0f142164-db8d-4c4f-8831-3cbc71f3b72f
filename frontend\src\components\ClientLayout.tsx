"use client";

import { ThemeProvider } from 'next-themes';
import Header from '@/components/layout/header';
import Footer from '@/components/layout/footer';
import { Toaster } from 'sonner';

interface AppLayoutProps {
  children: React.ReactNode;
  showHeader?: boolean;
  showFooter?: boolean;
}

export default function AppLayout({ children, showHeader = true, showFooter = true }: AppLayoutProps) {
  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <div className="min-h-screen flex flex-col">
        {showHeader && <Header />}
        <main className="flex-1">{children}</main>
        {showFooter && <Footer />}
      </div>
      <Toaster />
    </ThemeProvider>
  );
}