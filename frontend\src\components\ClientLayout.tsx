"use client";

import { ThemeProvider } from 'next-themes';
import dynamic from 'next/dynamic';

const DynamicHeader = dynamic(() => import('@/components/layout/header'), { ssr: false });
const DynamicFooter = dynamic(() => import('@/components/layout/footer'), { ssr: false });
const DynamicToaster = dynamic(() => import('sonner').then(mod => mod.Toaster), { ssr: false });

interface AppLayoutProps {
  children: React.ReactNode;
  showHeader?: boolean;
  showFooter?: boolean;
}

export default function AppLayout({ children, showHeader = true, showFooter = true }: AppLayoutProps) {
  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      {showHeader && <DynamicHeader />}
      <main className="flex-1 min-h-[60vh]">{children}</main>
      {showFooter && <DynamicFooter />}
      <DynamicToaster />
    </ThemeProvider>
  );
}