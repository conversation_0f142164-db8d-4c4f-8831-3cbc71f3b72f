// frontend/src/lib/validations/product.ts
import { z } from 'zod';
import { PRODUCT_CONFIG, UPLOAD_CONFIG } from '@/constants';

// Base validation schemas
export const priceSchema = z
  .number()
  .min(PRODUCT_CONFIG.MIN_PRICE, `Price must be at least $${PRODUCT_CONFIG.MIN_PRICE}`)
  .max(PRODUCT_CONFIG.MAX_PRICE, `Price cannot exceed $${PRODUCT_CONFIG.MAX_PRICE}`)
  .multipleOf(0.01, 'Price must be a valid currency amount');

export const stockSchema = z
  .number()
  .int('Stock must be a whole number')
  .min(PRODUCT_CONFIG.MIN_STOCK, `Stock cannot be negative`)
  .max(PRODUCT_CONFIG.MAX_STOCK, `Stock cannot exceed ${PRODUCT_CONFIG.MAX_STOCK}`);

export const productTitleSchema = z
  .string()
  .min(1, 'Product title is required')
  .max(PRODUCT_CONFIG.MAX_TITLE_LENGTH, `Title cannot exceed ${PRODUCT_CONFIG.MAX_TITLE_LENGTH} characters`)
  .trim();

export const productDescriptionSchema = z
  .string()
  .min(10, 'Description must be at least 10 characters')
  .max(PRODUCT_CONFIG.MAX_DESCRIPTION_LENGTH, `Description cannot exceed ${PRODUCT_CONFIG.MAX_DESCRIPTION_LENGTH} characters`)
  .trim();

export const categoryIdSchema = z
  .string()
  .min(1, 'Please select a category');

export const tagsSchema = z
  .array(z.string().min(1).max(50))
  .max(10, 'Maximum 10 tags allowed')
  .optional();

export const imageUrlSchema = z
  .string()
  .url('Please enter a valid image URL')
  .refine((url) => {
    const extension = url.split('.').pop()?.toLowerCase();
    return ['jpg', 'jpeg', 'png', 'webp', 'gif'].includes(extension || '');
  }, 'Image must be a valid format (JPG, PNG, WebP, or GIF)');

export const imagesSchema = z
  .array(imageUrlSchema)
  .min(1, 'At least one image is required')
  .max(PRODUCT_CONFIG.MAX_IMAGES, `Maximum ${PRODUCT_CONFIG.MAX_IMAGES} images allowed`);

// Product schemas
export const createProductSchema = z.object({
  name: productTitleSchema,
  title: productTitleSchema,
  description: productDescriptionSchema,
  price: priceSchema,
  stock: stockSchema,
  categoryId: categoryIdSchema,
  images: imagesSchema,
  tags: tagsSchema,
  specifications: z.record(z.string(), z.any()).optional(),
  status: z.enum(['active', 'inactive', 'draft']).default('draft'),
});

export const updateProductSchema = createProductSchema.partial().extend({
  id: z.string().min(1, 'Product ID is required'),
});

export const productFilterSchema = z.object({
  search: z.string().optional(),
  category: z.string().optional(),
  priceMin: z.number().min(0).optional(),
  priceMax: z.number().min(0).optional(),
  sortBy: z.enum(['price', 'date', 'popularity', 'rating']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(10),
});

export const productSearchSchema = z.object({
  query: z.string().min(1, 'Search query is required').max(100, 'Search query is too long'),
  filters: productFilterSchema.omit({ page: true, limit: true }).optional(),
});

// Category schemas
export const createCategorySchema = z.object({
  name: z.string().min(1, 'Category name is required').max(100, 'Category name is too long'),
  description: z.string().max(500, 'Description is too long').optional(),
  parentId: z.string().optional(),
  imageUrl: imageUrlSchema.optional(),
  slug: z.string()
    .min(1, 'Slug is required')
    .max(100, 'Slug is too long')
    .regex(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens'),
});

export const updateCategorySchema = createCategorySchema.partial().extend({
  id: z.string().min(1, 'Category ID is required'),
});

// File upload schemas
export const imageUploadSchema = z.object({
  file: z.instanceof(File, { message: 'Please select a file' })
    .refine((file) => file.size <= UPLOAD_CONFIG.MAX_IMAGE_SIZE, {
      message: `Image size must be less than ${UPLOAD_CONFIG.MAX_IMAGE_SIZE / 1024 / 1024}MB`,
    })
    .refine((file) => UPLOAD_CONFIG.ALLOWED_IMAGE_TYPES.includes(file.type as any), {
      message: 'Only JPEG, PNG, WebP, and GIF images are allowed',
    }),
  alt: z.string().max(200, 'Alt text is too long').optional(),
});

export const bulkImageUploadSchema = z.object({
  files: z.array(z.instanceof(File))
    .min(1, 'Please select at least one file')
    .max(PRODUCT_CONFIG.MAX_IMAGES, `Maximum ${PRODUCT_CONFIG.MAX_IMAGES} images allowed`)
    .refine((files) => {
      return files.every(file => file.size <= UPLOAD_CONFIG.MAX_IMAGE_SIZE);
    }, {
      message: `Each image must be less than ${UPLOAD_CONFIG.MAX_IMAGE_SIZE / 1024 / 1024}MB`,
    })
    .refine((files) => {
      return files.every(file => UPLOAD_CONFIG.ALLOWED_IMAGE_TYPES.includes(file.type as any));
    }, {
      message: 'Only JPEG, PNG, WebP, and GIF images are allowed',
    }),
});

// Product review schemas
export const createReviewSchema = z.object({
  productId: z.string().min(1, 'Product ID is required'),
  rating: z.number().int().min(1, 'Rating must be at least 1').max(5, 'Rating cannot exceed 5'),
  title: z.string().min(1, 'Review title is required').max(200, 'Title is too long'),
  comment: z.string().min(10, 'Comment must be at least 10 characters').max(1000, 'Comment is too long'),
  images: z.array(imageUrlSchema).max(5, 'Maximum 5 images allowed').optional(),
});

export const updateReviewSchema = createReviewSchema.partial().extend({
  id: z.string().min(1, 'Review ID is required'),
});

// Inventory schemas
export const updateStockSchema = z.object({
  productId: z.string().min(1, 'Product ID is required'),
  quantity: stockSchema,
  reason: z.string().max(500, 'Reason is too long').optional(),
});

export const bulkUpdateStockSchema = z.object({
  updates: z.array(updateStockSchema).min(1, 'At least one update is required').max(100, 'Maximum 100 updates allowed'),
});

// Type exports
export type CreateProductFormData = z.infer<typeof createProductSchema>;
export type UpdateProductFormData = z.infer<typeof updateProductSchema>;
export type ProductFilterData = z.infer<typeof productFilterSchema>;
export type ProductSearchData = z.infer<typeof productSearchSchema>;
export type CreateCategoryFormData = z.infer<typeof createCategorySchema>;
export type UpdateCategoryFormData = z.infer<typeof updateCategorySchema>;
export type ImageUploadData = z.infer<typeof imageUploadSchema>;
export type BulkImageUploadData = z.infer<typeof bulkImageUploadSchema>;
export type CreateReviewFormData = z.infer<typeof createReviewSchema>;
export type UpdateReviewFormData = z.infer<typeof updateReviewSchema>;
export type UpdateStockData = z.infer<typeof updateStockSchema>;
export type BulkUpdateStockData = z.infer<typeof bulkUpdateStockSchema>;
