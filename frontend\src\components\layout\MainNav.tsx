// frontend/src/components/layout/MainNav.tsx
'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { useAuthStore } from '@/store/authStore';
import { navRoutes } from '@/constants/navRoutes';

export function MainNav() {
  const pathname = usePathname();
  const { isAuthenticated, logout } = useAuthStore();

  return (
    <nav className="flex items-center space-x-4 lg:space-x-6" role="navigation" aria-label="Main navigation">
      {navRoutes.map((route) => {
        if (route.auth && !isAuthenticated) return null;
        const isActive = route.href === pathname || (route.href !== '/' && pathname.startsWith(route.href));
        return (
          <Link
            key={route.href}
            href={route.href}
            className={cn(
              'text-sm font-medium transition-colors hover:text-[var(--primary)]',
              isActive ? 'text-[var(--primary)]' : 'text-[var(--muted-foreground)]'
            )}
            aria-current={isActive ? 'page' : undefined}
          >
            {route.label}
          </Link>
        );
      })}
      {isAuthenticated && (
        <Link href="/dashboard/feed/new">
          <Button variant="outline" size="sm">Compose</Button>
        </Link>
      )}
      {isAuthenticated ? (
        <Button variant="outline" onClick={logout} size="sm">
          Logout
        </Button>
      ) : (
        <div className="flex items-center space-x-2">
          <Link href="/login">
            <Button variant="ghost" size="sm">Login</Button>
          </Link>
          <Link href="/register">
            <Button size="sm">Sign Up</Button>
          </Link>
        </div>
      )}
    </nav>
  );
}