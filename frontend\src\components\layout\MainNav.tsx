// frontend/src/components/layout/MainNav.tsx
'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { useAuthStore } from '@/store/authStore';
import { navRoutes } from '@/constants/navRoutes';

export function MainNav() {
  const pathname = usePathname();
  const { isAuthenticated } = useAuthStore();

  // Filter routes to show only main navigation items (not auth-specific dashboard items)
  const mainNavRoutes = navRoutes.filter(route =>
    !route.auth && !route.href.startsWith('/dashboard')
  );

  return (
    <nav className="flex items-center space-x-6" role="navigation" aria-label="Main navigation">
      {mainNavRoutes.map((route) => {
        const isActive = route.href === pathname || (route.href !== '/' && pathname.startsWith(route.href));
        return (
          <Link
            key={route.href}
            href={route.href}
            className={cn(
              'text-sm font-medium transition-colors hover:text-blue-400',
              isActive ? 'text-blue-400' : 'text-slate-300'
            )}
            aria-current={isActive ? 'page' : undefined}
          >
            {route.label}
          </Link>
        );
      })}
      {isAuthenticated && (
        <Link href="/dashboard/feed/new">
          <Button variant="outline" size="sm" className="border-slate-600 text-slate-300 hover:bg-slate-800 hover:text-white">
            Compose
          </Button>
        </Link>
      )}
    </nav>
  );
}