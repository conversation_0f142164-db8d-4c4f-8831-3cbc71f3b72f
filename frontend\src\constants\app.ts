// frontend/src/constants/app.ts

// Application Configuration
export const APP_CONFIG = {
  NAME: 'Market O\'Clock',
  DESCRIPTION: 'Your marketplace for everything',
  VERSION: '1.0.0',
  AUTHOR: 'Market O\'Clock Team',
  KEYWORDS: ['marketplace', 'ecommerce', 'social', 'trading'],
  URL: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
  SUPPORT_EMAIL: '<EMAIL>',
  CONTACT_EMAIL: '<EMAIL>',
} as const;

// Theme Configuration
export const THEME_CONFIG = {
  DEFAULT_THEME: 'light',
  THEMES: ['light', 'dark', 'system'] as const,
  STORAGE_KEY: 'theme-preference',
} as const;

// Pagination Configuration
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 100,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100] as const,
} as const;

// File Upload Configuration
export const UPLOAD_CONFIG = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_IMAGE_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'] as const,
  ALLOWED_FILE_TYPES: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
  ] as const,
  IMAGE_QUALITY: 0.8,
  THUMBNAIL_SIZE: { width: 200, height: 200 },
  PREVIEW_SIZE: { width: 800, height: 600 },
} as const;

// User Roles and Permissions
export const USER_ROLES = {
  ADMIN: 'admin',
  BUYER: 'buyer',
  SUPPLIER: 'supplier',
  MODERATOR: 'moderator',
} as const;

export const PERMISSIONS = {
  // Product permissions
  CREATE_PRODUCT: 'create_product',
  EDIT_PRODUCT: 'edit_product',
  DELETE_PRODUCT: 'delete_product',
  VIEW_PRODUCT: 'view_product',
  
  // Order permissions
  CREATE_ORDER: 'create_order',
  VIEW_ORDER: 'view_order',
  MANAGE_ORDER: 'manage_order',
  
  // User permissions
  VIEW_USER: 'view_user',
  EDIT_USER: 'edit_user',
  DELETE_USER: 'delete_user',
  
  // Admin permissions
  ADMIN_DASHBOARD: 'admin_dashboard',
  MANAGE_USERS: 'manage_users',
  MANAGE_PRODUCTS: 'manage_products',
  MANAGE_ORDERS: 'manage_orders',
  VIEW_ANALYTICS: 'view_analytics',
} as const;

// Product Configuration
export const PRODUCT_CONFIG = {
  MIN_PRICE: 0.01,
  MAX_PRICE: 999999.99,
  MAX_IMAGES: 10,
  MAX_TITLE_LENGTH: 100,
  MAX_DESCRIPTION_LENGTH: 2000,
  MIN_STOCK: 0,
  MAX_STOCK: 999999,
  FEATURED_LIMIT: 12,
  TRENDING_LIMIT: 20,
} as const;

// Order Configuration
export const ORDER_CONFIG = {
  STATUSES: [
    'pending',
    'confirmed',
    'processing',
    'shipped',
    'delivered',
    'cancelled',
    'refunded',
  ] as const,
  PAYMENT_METHODS: [
    'credit_card',
    'debit_card',
    'paypal',
    'bank_transfer',
    'cash_on_delivery',
  ] as const,
  SHIPPING_METHODS: [
    'standard',
    'express',
    'overnight',
    'pickup',
  ] as const,
  TAX_RATE: 0.08, // 8%
  SHIPPING_RATES: {
    standard: 10,
    express: 20,
    overnight: 35,
    pickup: 0,
  },
  FREE_SHIPPING_THRESHOLD: 100,
} as const;

// Social Features Configuration
export const SOCIAL_CONFIG = {
  MAX_POST_LENGTH: 2000,
  MAX_MICROBLOG_LENGTH: 280,
  MAX_COMMENT_LENGTH: 500,
  MAX_BIO_LENGTH: 160,
  POSTS_PER_PAGE: 10,
  COMMENTS_PER_PAGE: 20,
  FOLLOWERS_PER_PAGE: 50,
} as const;

// Notification Configuration
export const NOTIFICATION_CONFIG = {
  TYPES: [
    'order',
    'message',
    'like',
    'comment',
    'follow',
    'system',
    'promotion',
  ] as const,
  MAX_NOTIFICATIONS: 100,
  AUTO_MARK_READ_DELAY: 5000, // 5 seconds
  PUSH_ENABLED: true,
} as const;

// Search Configuration
export const SEARCH_CONFIG = {
  MIN_QUERY_LENGTH: 2,
  MAX_QUERY_LENGTH: 100,
  DEBOUNCE_DELAY: 300, // milliseconds
  MAX_SUGGESTIONS: 10,
  MAX_RECENT_SEARCHES: 5,
  HIGHLIGHT_CLASS: 'search-highlight',
} as const;

// Cache Configuration
export const CACHE_CONFIG = {
  DEFAULT_TTL: 5 * 60 * 1000, // 5 minutes
  USER_TTL: 15 * 60 * 1000, // 15 minutes
  PRODUCT_TTL: 10 * 60 * 1000, // 10 minutes
  CATEGORY_TTL: 30 * 60 * 1000, // 30 minutes
  STATIC_TTL: 60 * 60 * 1000, // 1 hour
} as const;

// Animation Configuration
export const ANIMATION_CONFIG = {
  DURATION: {
    FAST: 150,
    NORMAL: 300,
    SLOW: 500,
  },
  EASING: {
    EASE_IN: 'ease-in',
    EASE_OUT: 'ease-out',
    EASE_IN_OUT: 'ease-in-out',
    BOUNCE: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  },
} as const;

// Validation Configuration
export const VALIDATION_CONFIG = {
  PASSWORD: {
    MIN_LENGTH: 8,
    MAX_LENGTH: 128,
    REQUIRE_UPPERCASE: true,
    REQUIRE_LOWERCASE: true,
    REQUIRE_NUMBERS: true,
    REQUIRE_SYMBOLS: false,
  },
  USERNAME: {
    MIN_LENGTH: 3,
    MAX_LENGTH: 30,
    PATTERN: /^[a-zA-Z0-9_-]+$/,
  },
  EMAIL: {
    PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  },
  PHONE: {
    PATTERN: /^\+?[\d\s\-\(\)]+$/,
  },
} as const;

// Local Storage Keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth-token',
  REFRESH_TOKEN: 'refresh-token',
  USER_DATA: 'user-data',
  CART_ITEMS: 'cart-items',
  THEME: 'theme-preference',
  LANGUAGE: 'language-preference',
  RECENT_SEARCHES: 'recent-searches',
  VIEWED_PRODUCTS: 'viewed-products',
  PREFERENCES: 'user-preferences',
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection.',
  SERVER_ERROR: 'Server error. Please try again later.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  FORBIDDEN: 'Access denied.',
  NOT_FOUND: 'The requested resource was not found.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  TIMEOUT_ERROR: 'Request timed out. Please try again.',
  UNKNOWN_ERROR: 'An unexpected error occurred.',
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'Successfully logged in!',
  LOGOUT_SUCCESS: 'Successfully logged out!',
  REGISTER_SUCCESS: 'Account created successfully!',
  PROFILE_UPDATED: 'Profile updated successfully!',
  PRODUCT_CREATED: 'Product created successfully!',
  PRODUCT_UPDATED: 'Product updated successfully!',
  ORDER_PLACED: 'Order placed successfully!',
  ITEM_ADDED_TO_CART: 'Item added to cart!',
  ITEM_REMOVED_FROM_CART: 'Item removed from cart!',
} as const;
