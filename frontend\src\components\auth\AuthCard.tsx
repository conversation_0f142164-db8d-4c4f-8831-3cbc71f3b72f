import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { cn } from '@/lib/utils';

interface AuthCardProps {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  error?: string | null;
  children: React.ReactNode;
  className?: string;
}

export function AuthCard({ 
  title, 
  description, 
  icon, 
  error, 
  children, 
  className 
}: AuthCardProps) {
  return (
    <div className="flex-grow flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <Card className={cn("w-full max-w-md", className)}>
        <CardHeader className="text-center">
          {icon && (
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/20">
              {icon}
            </div>
          )}
          <CardTitle className="text-2xl">{title}</CardTitle>
          {description && (
            <CardDescription>{description}</CardDescription>
          )}
        </CardHeader>
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          {children}
        </CardContent>
      </Card>
    </div>
  );
}
