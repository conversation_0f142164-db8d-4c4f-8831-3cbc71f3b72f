"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { registerSchema } from '@/lib/validations/auth';
import { useAuthStore } from '@/store/authStore';
import { Button } from '@/components/ui/button';
import { AuthCard } from '@/components/auth/AuthCard';
import { FormField } from '@/components/auth/FormField';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { UserPlus } from 'lucide-react';
import type { z } from 'zod';

type RegisterFormData = z.infer<typeof registerSchema>;

export default function RegisterPage() {
  const router = useRouter();
  const { register: registerUser, isLoading, error, clearError, isAuthenticated } = useAuthStore();

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      role: 'buyer',
      agreeToTerms: false,
    },
  });

  const watchRole = watch('role');

  useEffect(() => {
    if (isAuthenticated) {
      router.push('/dashboard');
    }
  }, [isAuthenticated, router]);

  const onSubmit = async (data: RegisterFormData) => {
    clearError();
    try {
      await registerUser(
        data.username,
        data.email,
        data.password,
        data.role,
        data.fullName,
        data.businessName
      );
      router.push('/dashboard');
    } catch (err) {
      // Error is handled by the store
    }
  };

  return (
    <AuthCard
      title="Register for Market O'Clock"
      description={
        <>
          Already have an account?{' '}
          <Link href="/login" className="font-medium text-primary hover:text-primary/80 transition-colors">
            Sign in
          </Link>
        </>
      }
      icon={<UserPlus className="h-6 w-6 text-blue-600 dark:text-blue-400" />}
      error={error}
    >

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          id="username"
          label="Username"
          type="text"
          placeholder="Choose a username"
          autoComplete="username"
          error={errors.username?.message}
          {...register('username')}
        />

        <FormField
          id="email"
          label="Email address"
          type="email"
          placeholder="Enter your email address"
          autoComplete="email"
          error={errors.email?.message}
          {...register('email')}
        />
        <FormField
          id="password"
          label="Password"
          placeholder="Create a strong password"
          autoComplete="new-password"
          isPassword
          error={errors.password?.message}
          {...register('password')}
        />

        <FormField
          id="confirmPassword"
          label="Confirm password"
          placeholder="Confirm your password"
          autoComplete="new-password"
          isPassword
          error={errors.confirmPassword?.message}
          {...register('confirmPassword')}
        />

        <div className="space-y-2">
          <label htmlFor="role" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
            Role
          </label>
          <Select value={watchRole} onValueChange={(value) => setValue('role', value as 'buyer' | 'supplier')}>
            <SelectTrigger>
              <SelectValue placeholder="Select your role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="buyer">Buyer</SelectItem>
              <SelectItem value="supplier">Supplier</SelectItem>
            </SelectContent>
          </Select>
          {errors.role && (
            <p className="text-sm text-destructive">{errors.role.message}</p>
          )}
        </div>
        <FormField
          id="fullName"
          label="Full Name (Optional)"
          type="text"
          placeholder="Enter your full name"
          autoComplete="name"
          error={errors.fullName?.message}
          {...register('fullName')}
        />

        <FormField
          id="businessName"
          label="Business Name (Optional)"
          type="text"
          placeholder="Enter your business name"
          autoComplete="organization"
          error={errors.businessName?.message}
          {...register('businessName')}
        />

        <div className="flex items-center space-x-2">
          <input
            id="agreeToTerms"
            type="checkbox"
            className="h-4 w-4 text-primary focus:ring-ring border-border rounded"
            {...register('agreeToTerms')}
          />
          <label htmlFor="agreeToTerms" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
            I agree to the{' '}
            <Link href="/terms" className="text-primary hover:text-primary/80 underline">
              Terms of Service
            </Link>{' '}
            and{' '}
            <Link href="/privacy" className="text-primary hover:text-primary/80 underline">
              Privacy Policy
            </Link>
          </label>
        </div>
        {errors.agreeToTerms && (
          <p className="text-sm text-destructive">{errors.agreeToTerms.message}</p>
        )}

        <Button type="submit" className="w-full" disabled={isLoading}>
          {isLoading ? 'Creating account...' : 'Create account'}
        </Button>
      </form>
    </AuthCard>
  );
}