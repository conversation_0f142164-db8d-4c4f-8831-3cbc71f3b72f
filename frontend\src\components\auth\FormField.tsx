import React from 'react';
import { Input } from '@/components/ui/input';
import { PasswordInput } from './PasswordInput';
import { cn } from '@/lib/utils';

interface FormFieldProps extends React.ComponentProps<typeof Input> {
  label: string;
  error?: string;
  isPassword?: boolean;
  showPasswordToggle?: boolean;
}

export function FormField({ 
  label, 
  error, 
  isPassword = false, 
  showPasswordToggle = true,
  className,
  ...props 
}: FormFieldProps) {
  const InputComponent = isPassword ? PasswordInput : Input;
  
  return (
    <div className="space-y-2">
      <label 
        htmlFor={props.id} 
        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
      >
        {label}
      </label>
      <InputComponent
        className={cn(error && "border-destructive", className)}
        aria-invalid={error ? 'true' : 'false'}
        showToggle={isPassword ? showPasswordToggle : undefined}
        {...props}
      />
      {error && (
        <p className="text-sm text-destructive">{error}</p>
      )}
    </div>
  );
}
