// frontend/src/types/index.ts

// ============================================================================
// CORE TYPES
// ============================================================================

export type ID = string;
export type Timestamp = string; // ISO 8601 format
export type URL = string;
export type Email = string;

// ============================================================================
// USER & AUTHENTICATION TYPES
// ============================================================================

export type UserRole = 'buyer' | 'supplier' | 'admin';

export interface User {
  id: ID;
  username: string;
  email: Email;
  role: UserRole;
  name?: string;
  full_name?: string;
  business_name?: string;
  bio?: string;
  phoneNumber?: string;
  address?: string;
  avatarUrl?: URL;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken?: string;
  expiresAt?: Timestamp;
}

export interface LoginCredentials {
  email: Email;
  password: string;
}

export interface RegisterData {
  username: string;
  email: Email;
  password: string;
  role: UserRole;
  fullName?: string;
  businessName?: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken?: string;
}

// ============================================================================
// PRODUCT TYPES
// ============================================================================

export type ProductStatus = 'active' | 'inactive' | 'draft' | 'sold';

export interface Product {
  id: ID;
  name: string;
  title: string;
  description: string;
  price: number;
  stock: number;
  images: URL[];
  categoryId: ID;
  sellerId: ID;
  seller: {
    id: ID;
    name: string;
    business_name?: string;
  };
  status: ProductStatus;
  tags?: string[];
  specifications?: Record<string, any>;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface ProductImage {
  id: ID;
  productId: ID;
  imageUrl: URL;
  altText?: string;
  order: number;
  createdAt: Timestamp;
}

export interface Category {
  id: ID;
  name: string;
  description?: string;
  parentId?: ID;
  slug: string;
  imageUrl?: URL;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// ============================================================================
// ORDER & COMMERCE TYPES
// ============================================================================

export type OrderStatus = 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';
export type PaymentStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'refunded';
export type PaymentMethod = 'credit_card' | 'debit_card' | 'paypal' | 'bank_transfer' | 'cash_on_delivery';

export interface OrderItem {
  id: ID;
  productId: ID;
  product: Product;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

export interface Order {
  id: ID;
  userId: ID;
  items: OrderItem[];
  subtotal: number;
  tax: number;
  shipping: number;
  total: number;
  status: OrderStatus;
  paymentStatus: PaymentStatus;
  paymentMethod?: PaymentMethod;
  shippingAddress: Address;
  billingAddress?: Address;
  notes?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface Address {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  isDefault?: boolean;
}

export interface PaymentDetails {
  cardNumber: string;
  expiryDate: string;
  cvv: string;
  cardholderName: string;
  billingAddress: Address;
}

// ============================================================================
// SOCIAL & CONTENT TYPES
// ============================================================================

export interface Post {
  id: ID;
  title: string;
  content: string;
  authorId: ID;
  author: {
    id: ID;
    name: string;
    avatarUrl?: URL;
  };
  categoryId?: ID;
  tags?: string[];
  imageUrl?: URL;
  published: boolean;
  likesCount: number;
  commentsCount: number;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface MicroblogPost {
  id: ID;
  content: string;
  authorId: ID;
  author: {
    id: ID;
    name: string;
    avatarUrl?: URL;
  };
  likes: number;
  comments: number;
  imageUrl?: URL;
  createdAt: Timestamp;
}

export interface Comment {
  id: ID;
  postId: ID;
  authorId: ID;
  author: {
    id: ID;
    name: string;
    avatarUrl?: URL;
  };
  content: string;
  parentId?: ID; // For nested comments
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface Like {
  id: ID;
  userId: ID;
  postId: ID;
  createdAt: Timestamp;
}

// ============================================================================
// NOTIFICATION TYPES
// ============================================================================

export type NotificationType = 'order' | 'message' | 'like' | 'comment' | 'follow' | 'system';
export type NotificationStatus = 'unread' | 'read' | 'archived';

export interface Notification {
  id: ID;
  userId: ID;
  type: NotificationType;
  title: string;
  message: string;
  status: NotificationStatus;
  actionUrl?: URL;
  metadata?: Record<string, any>;
  createdAt: Timestamp;
  readAt?: Timestamp;
}

// ============================================================================
// API RESPONSE TYPES
// ============================================================================

export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  errors?: string[];
}

export interface PaginationMeta {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface PaginatedResponse<T> {
  items: T[];
  meta: PaginationMeta;
}

export interface ApiError {
  message: string;
  code?: string;
  field?: string;
  details?: Record<string, any>;
}

// ============================================================================
// FORM & UI TYPES
// ============================================================================

export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'textarea' | 'select' | 'checkbox' | 'file';
  required?: boolean;
  placeholder?: string;
  options?: { value: string; label: string }[];
  validation?: Record<string, any>;
}

export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface FileUpload {
  file: File;
  preview?: string;
  progress?: number;
  error?: string;
}

// ============================================================================
// SEARCH & FILTER TYPES
// ============================================================================

export interface SearchFilters {
  query?: string;
  category?: ID;
  priceMin?: number;
  priceMax?: number;
  location?: string;
  sortBy?: 'price' | 'date' | 'popularity' | 'rating';
  sortOrder?: 'asc' | 'desc';
}

export interface SearchResult<T> {
  items: T[];
  total: number;
  filters: SearchFilters;
  suggestions?: string[];
}

// ============================================================================
// CART TYPES
// ============================================================================

export interface CartItem {
  productId: ID;
  product: Product;
  quantity: number;
  selectedVariant?: string;
  addedAt: Timestamp;
}

export interface Cart {
  id: ID;
  userId?: ID;
  items: CartItem[];
  subtotal: number;
  tax: number;
  shipping: number;
  total: number;
  updatedAt: Timestamp;
}

// ============================================================================
// SELLER TYPES
// ============================================================================

export interface Seller {
  id: ID;
  userId: ID;
  name: string;
  businessName?: string;
  description?: string;
  logoUrl?: URL;
  rating: number;
  reviewsCount: number;
  productsCount: number;
  verified: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// ============================================================================
// ANALYTICS TYPES
// ============================================================================

export interface AnalyticsData {
  views: number;
  clicks: number;
  conversions: number;
  revenue: number;
  period: 'day' | 'week' | 'month' | 'year';
  date: Timestamp;
}

export interface DashboardStats {
  totalProducts: number;
  totalOrders: number;
  totalRevenue: number;
  totalCustomers: number;
  recentOrders: Order[];
  topProducts: Product[];
  analytics: AnalyticsData[];
}
