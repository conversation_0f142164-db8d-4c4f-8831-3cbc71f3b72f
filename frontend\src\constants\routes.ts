// frontend/src/constants/routes.ts

// Public Routes
export const PUBLIC_ROUTES = {
  HOME: '/',
  ABOUT: '/about',
  CONTACT: '/contact',
  CAREERS: '/careers',
  PRIVACY: '/privacy',
  TERMS: '/terms',
  BLOG: '/blog',
  EXPLORE: '/explore',
  POPULAR: '/popular',
  CATEGORIES: '/categories',
  MARKETPLACE: '/marketplace',
  PRODUCT: (id: string) => `/product/${id}`,
  POST: (id: string) => `/post/${id}`,
  PROMOTION: '/promotion',
} as const;

// Authentication Routes
export const AUTH_ROUTES = {
  LOGIN: '/login',
  REGISTER: '/register',
  FORGOT_PASSWORD: '/forgot-password',
  RESET_PASSWORD: '/reset-password',
  VERIFY_EMAIL: '/verify-email',
} as const;

// Protected Routes (require authentication)
export const PROTECTED_ROUTES = {
  DASHBOARD: '/dashboard',
  CHECKOUT: '/checkout',
  ORDER: '/order',
  NEW_POST: '/new',
  NEW_BLOG: '/blogs/new',
  NEW_MARKETPLACE: '/marketplace/new',
} as const;

// Dashboard Routes
export const DASHBOARD_ROUTES = {
  HOME: '/dashboard',
  PROFILE: '/dashboard/profile',
  PROFILE_SETTINGS: '/dashboard/profile/settings',
  PRODUCTS: '/dashboard/products',
  ADD_PRODUCT: '/dashboard/products/add',
  EDIT_PRODUCT: (id: string) => `/dashboard/products/${id}/edit`,
  MARKETPLACE: '/dashboard/marketplace',
  FEED: '/dashboard/feed',
  NEW_FEED_POST: '/dashboard/feed/new',
  FEED_POST: (id: string) => `/dashboard/feed/${id}`,
  NETWORK: '/dashboard/network',
  NOTIFICATIONS: '/dashboard/notifications',
  ORDERS: '/dashboard/orders',
  SETTINGS: '/dashboard/settings',
  LISTINGS: '/dashboard/listings',
} as const;

// API Routes (for internal use)
export const API_ROUTES = {
  AUTH: '/api/auth',
  USERS: '/api/users',
  PRODUCTS: '/api/products',
  ORDERS: '/api/orders',
  POSTS: '/api/posts',
  COMMENTS: '/api/comments',
  CATEGORIES: '/api/categories',
  UPLOAD: '/api/upload',
  SEARCH: '/api/search',
  NOTIFICATIONS: '/api/notifications',
} as const;

// External Routes
export const EXTERNAL_ROUTES = {
  GITHUB: 'https://github.com/marketoclock',
  TWITTER: 'https://twitter.com/marketoclock',
  FACEBOOK: 'https://facebook.com/marketoclock',
  INSTAGRAM: 'https://instagram.com/marketoclock',
  LINKEDIN: 'https://linkedin.com/company/marketoclock',
  SUPPORT: 'mailto:<EMAIL>',
  DOCUMENTATION: 'https://docs.marketoclock.com',
} as const;

// Route Groups for easier management
export const ROUTE_GROUPS = {
  PUBLIC: Object.values(PUBLIC_ROUTES),
  AUTH: Object.values(AUTH_ROUTES),
  PROTECTED: Object.values(PROTECTED_ROUTES),
  DASHBOARD: Object.values(DASHBOARD_ROUTES),
} as const;

// Navigation Menu Items
export const MAIN_NAVIGATION = [
  { label: 'Home', href: PUBLIC_ROUTES.HOME },
  { label: 'Marketplace', href: PUBLIC_ROUTES.MARKETPLACE },
  { label: 'Explore', href: PUBLIC_ROUTES.EXPLORE },
  { label: 'Popular', href: PUBLIC_ROUTES.POPULAR },
  { label: 'Blog', href: PUBLIC_ROUTES.BLOG },
  { label: 'About', href: PUBLIC_ROUTES.ABOUT },
  { label: 'Contact', href: PUBLIC_ROUTES.CONTACT },
] as const;

export const DASHBOARD_NAVIGATION = [
  { 
    label: 'Dashboard', 
    href: DASHBOARD_ROUTES.HOME,
    icon: 'Home',
  },
  { 
    label: 'Products', 
    href: DASHBOARD_ROUTES.PRODUCTS,
    icon: 'Package',
  },
  { 
    label: 'Marketplace', 
    href: DASHBOARD_ROUTES.MARKETPLACE,
    icon: 'ShoppingBag',
  },
  { 
    label: 'Social Feed', 
    href: DASHBOARD_ROUTES.FEED,
    icon: 'MessageSquare',
  },
  { 
    label: 'Network', 
    href: DASHBOARD_ROUTES.NETWORK,
    icon: 'Users',
  },
  { 
    label: 'Notifications', 
    href: DASHBOARD_ROUTES.NOTIFICATIONS,
    icon: 'Bell',
  },
  { 
    label: 'Orders', 
    href: DASHBOARD_ROUTES.ORDERS,
    icon: 'ShoppingCart',
  },
  { 
    label: 'Profile', 
    href: DASHBOARD_ROUTES.PROFILE,
    icon: 'User',
  },
] as const;

export const FOOTER_NAVIGATION = {
  COMPANY: [
    { label: 'About Us', href: PUBLIC_ROUTES.ABOUT },
    { label: 'Careers', href: PUBLIC_ROUTES.CAREERS },
    { label: 'Contact', href: PUBLIC_ROUTES.CONTACT },
    { label: 'Blog', href: PUBLIC_ROUTES.BLOG },
  ],
  SUPPORT: [
    { label: 'Help Center', href: '/help' },
    { label: 'Safety', href: '/safety' },
    { label: 'Community Guidelines', href: '/guidelines' },
    { label: 'Report Issue', href: '/report' },
  ],
  LEGAL: [
    { label: 'Privacy Policy', href: PUBLIC_ROUTES.PRIVACY },
    { label: 'Terms of Service', href: PUBLIC_ROUTES.TERMS },
    { label: 'Cookie Policy', href: '/cookies' },
    { label: 'Accessibility', href: '/accessibility' },
  ],
  SOCIAL: [
    { label: 'Twitter', href: EXTERNAL_ROUTES.TWITTER },
    { label: 'Facebook', href: EXTERNAL_ROUTES.FACEBOOK },
    { label: 'Instagram', href: EXTERNAL_ROUTES.INSTAGRAM },
    { label: 'LinkedIn', href: EXTERNAL_ROUTES.LINKEDIN },
  ],
} as const;

// Breadcrumb configurations
export const BREADCRUMB_CONFIG = {
  [PUBLIC_ROUTES.HOME]: [],
  [PUBLIC_ROUTES.MARKETPLACE]: [
    { label: 'Home', href: PUBLIC_ROUTES.HOME },
    { label: 'Marketplace', href: PUBLIC_ROUTES.MARKETPLACE },
  ],
  [PUBLIC_ROUTES.BLOG]: [
    { label: 'Home', href: PUBLIC_ROUTES.HOME },
    { label: 'Blog', href: PUBLIC_ROUTES.BLOG },
  ],
  [DASHBOARD_ROUTES.HOME]: [
    { label: 'Dashboard', href: DASHBOARD_ROUTES.HOME },
  ],
  [DASHBOARD_ROUTES.PRODUCTS]: [
    { label: 'Dashboard', href: DASHBOARD_ROUTES.HOME },
    { label: 'Products', href: DASHBOARD_ROUTES.PRODUCTS },
  ],
  [DASHBOARD_ROUTES.ADD_PRODUCT]: [
    { label: 'Dashboard', href: DASHBOARD_ROUTES.HOME },
    { label: 'Products', href: DASHBOARD_ROUTES.PRODUCTS },
    { label: 'Add Product', href: DASHBOARD_ROUTES.ADD_PRODUCT },
  ],
} as const;

// Route metadata for SEO and page titles
export const ROUTE_METADATA = {
  [PUBLIC_ROUTES.HOME]: {
    title: 'Market O\'Clock - Your Marketplace for Everything',
    description: 'Discover amazing products and connect with sellers on Market O\'Clock.',
  },
  [PUBLIC_ROUTES.MARKETPLACE]: {
    title: 'Marketplace - Market O\'Clock',
    description: 'Browse thousands of products from verified sellers.',
  },
  [PUBLIC_ROUTES.BLOG]: {
    title: 'Blog - Market O\'Clock',
    description: 'Read the latest news, tips, and insights from our community.',
  },
  [DASHBOARD_ROUTES.HOME]: {
    title: 'Dashboard - Market O\'Clock',
    description: 'Manage your products, orders, and profile.',
  },
  [DASHBOARD_ROUTES.PRODUCTS]: {
    title: 'My Products - Market O\'Clock',
    description: 'Manage your product listings and inventory.',
  },
} as const;
