// frontend/src/components/layout/header.tsx
'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ModeToggle } from '@/components/mode-toggle';
import { Search, ShoppingCart } from 'lucide-react';
import { MainNav } from './MainNav';
import { MobileNav } from './MobileNav';
import { useAuthStore } from '@/store/authStore';

export default function Header() {
  const { isAuthenticated } = useAuthStore();

  return (
    <header className="w-full border-b border-slate-800 bg-slate-900 text-white sticky top-0 z-50" aria-label="Site header">
      <div className="max-w-7xl mx-auto px-4 py-4 flex justify-between items-center">
        <div className="flex items-center gap-6">
          <Link
            href="/"
            className="font-bold text-xl bg-gradient-to-r from-blue-500 to-purple-500 bg-clip-text text-transparent hover:from-blue-400 hover:to-purple-400 transition-all duration-200"
            aria-label="Market O'Clock Home"
          >
            Market O'Clock
          </Link>
          <nav className="hidden md:block" aria-label="Main navigation">
            <MainNav />
          </nav>
        </div>

        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="icon"
            className="text-slate-300 hover:text-blue-400 hover:bg-slate-800 transition-colors"
            aria-label="Search"
          >
            <Search className="h-5 w-5" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="text-slate-300 hover:text-blue-400 hover:bg-slate-800 transition-colors"
            aria-label="Shopping Cart"
          >
            <ShoppingCart className="h-5 w-5" />
          </Button>
          <ModeToggle />

          {/* Auth Buttons */}
          <div className="hidden md:flex items-center gap-3">
            {isAuthenticated ? (
              <Link href="/dashboard">
                <Button variant="outline" size="sm" className="border-slate-600 text-slate-300 hover:bg-slate-800 hover:text-white">
                  Dashboard
                </Button>
              </Link>
            ) : (
              <>
                <Link href="/login">
                  <Button variant="ghost" size="sm" className="text-slate-300 hover:text-blue-400 hover:bg-slate-800">
                    Login
                  </Button>
                </Link>
                <Link href="/register">
                  <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
                    Sign Up
                  </Button>
                </Link>
              </>
            )}
          </div>

          <div className="md:hidden">
            <MobileNav />
          </div>
        </div>
      </div>
    </header>
  );
}