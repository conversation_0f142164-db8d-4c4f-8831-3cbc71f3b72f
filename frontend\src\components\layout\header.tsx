// frontend/src/components/layout/header.tsx
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ModeToggle } from '@/components/mode-toggle';
import { Search, ShoppingCart } from 'lucide-react';
import { MainNav } from './MainNav';
import { MobileNav } from './MobileNav';

export default function Header() {
  return (
    <header className="bg-[var(--card)] border-b shadow-sm sticky top-0 z-50" aria-label="Site header">
      <div className="container flex h-16 items-center justify-between px-4">
        <div className="flex items-center gap-6">
          <Link href="/" className="text-xl font-semibold text-[var(--primary)]" aria-label="Market O'Clock Home">
            Market O'Clock
          </Link>
          <nav className="hidden md:block" aria-label="Main navigation">
            <MainNav />
          </nav>
        </div>
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" className="text-[var(--foreground)] hover:text-[var(--primary)]" aria-label="Search">
            <Search className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" className="text-[var(--foreground)] hover:text-[var(--primary)]" aria-label="Shopping Cart">
            <ShoppingCart className="h-4 w-4" />
          </Button>
          <ModeToggle />
          <div className="md:hidden">
            <MobileNav />
          </div>
        </div>
      </div>
    </header>
  );
}