<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Market O'Clock</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .container {
            text-align: center;
            background: white;
            padding: 3rem 2rem;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 90%;
        }

        .icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 2rem;
            background: #f3f4f6;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
        }

        h1 {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: #1f2937;
        }

        p {
            font-size: 1.1rem;
            color: #6b7280;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        button:hover {
            background: #1d4ed8;
        }

        button.secondary {
            background: #f3f4f6;
            color: #374151;
        }

        button.secondary:hover {
            background: #e5e7eb;
        }

        .status {
            margin-top: 2rem;
            padding: 1rem;
            background: #fef3c7;
            border-radius: 8px;
            color: #92400e;
            font-size: 0.9rem;
        }

        .status.online {
            background: #d1fae5;
            color: #065f46;
        }

        @media (max-width: 480px) {
            .container {
                padding: 2rem 1.5rem;
            }

            h1 {
                font-size: 1.5rem;
            }

            .buttons {
                flex-direction: column;
            }

            button {
                width: 100%;
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon pulse">
            📡
        </div>
        
        <h1>You're Offline</h1>
        
        <p>
            It looks like you've lost your internet connection. 
            Don't worry, you can still browse some cached content 
            or try reconnecting.
        </p>

        <div class="buttons">
            <button onclick="tryReconnect()">
                Try Again
            </button>
            <button class="secondary" onclick="goHome()">
                Go Home
            </button>
        </div>

        <div id="status" class="status">
            Checking connection...
        </div>
    </div>

    <script>
        // Check online status
        function updateStatus() {
            const statusEl = document.getElementById('status');
            if (navigator.onLine) {
                statusEl.textContent = 'Connection restored! You can now browse normally.';
                statusEl.className = 'status online';
            } else {
                statusEl.textContent = 'Still offline. Please check your internet connection.';
                statusEl.className = 'status';
            }
        }

        // Try to reconnect
        function tryReconnect() {
            if (navigator.onLine) {
                window.location.reload();
            } else {
                updateStatus();
                // Try to reload after a short delay
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            }
        }

        // Go to home page
        function goHome() {
            window.location.href = '/';
        }

        // Listen for online/offline events
        window.addEventListener('online', () => {
            updateStatus();
            // Auto-reload when connection is restored
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        });

        window.addEventListener('offline', updateStatus);

        // Initial status check
        updateStatus();

        // Periodic connection check
        setInterval(() => {
            if (navigator.onLine) {
                // Try to fetch a small resource to verify connection
                fetch('/manifest.json', { 
                    method: 'HEAD',
                    cache: 'no-cache'
                })
                .then(() => {
                    updateStatus();
                })
                .catch(() => {
                    // Connection might be limited
                    const statusEl = document.getElementById('status');
                    statusEl.textContent = 'Limited connectivity detected. Some features may not work.';
                    statusEl.className = 'status';
                });
            }
        }, 5000);

        // Service worker registration check
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.ready.then((registration) => {
                console.log('Service Worker is ready');
            });
        }
    </script>
</body>
</html>
