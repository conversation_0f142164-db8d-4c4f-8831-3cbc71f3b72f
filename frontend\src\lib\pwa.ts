// frontend/src/lib/pwa.ts
'use client';

// PWA utilities and service worker management

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

interface PWAInstallPrompt {
  isInstallable: boolean;
  isInstalled: boolean;
  prompt: (() => Promise<void>) | null;
  outcome: string | null;
}

// Service Worker registration
export async function registerServiceWorker(): Promise<ServiceWorkerRegistration | null> {
  if (typeof window === 'undefined' || !('serviceWorker' in navigator)) {
    console.log('Service Worker not supported');
    return null;
  }

  try {
    const registration = await navigator.serviceWorker.register('/sw.js', {
      scope: '/',
    });

    console.log('Service Worker registered successfully:', registration);

    // Handle updates
    registration.addEventListener('updatefound', () => {
      const newWorker = registration.installing;
      if (newWorker) {
        newWorker.addEventListener('statechange', () => {
          if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
            // New content is available
            showUpdateNotification();
          }
        });
      }
    });

    return registration;
  } catch (error) {
    console.error('Service Worker registration failed:', error);
    return null;
  }
}

// Unregister service worker
export async function unregisterServiceWorker(): Promise<boolean> {
  if (typeof window === 'undefined' || !('serviceWorker' in navigator)) {
    return false;
  }

  try {
    const registration = await navigator.serviceWorker.getRegistration();
    if (registration) {
      const result = await registration.unregister();
      console.log('Service Worker unregistered:', result);
      return result;
    }
    return false;
  } catch (error) {
    console.error('Service Worker unregistration failed:', error);
    return false;
  }
}

// Check if app is installed
export function isAppInstalled(): boolean {
  if (typeof window === 'undefined') return false;
  
  return (
    window.matchMedia('(display-mode: standalone)').matches ||
    window.matchMedia('(display-mode: fullscreen)').matches ||
    (window.navigator as any).standalone === true
  );
}

// PWA install prompt management
export function usePWAInstall(): PWAInstallPrompt {
  if (typeof window === 'undefined') {
    return {
      isInstallable: false,
      isInstalled: false,
      prompt: null,
      outcome: null,
    };
  }

  let deferredPrompt: BeforeInstallPromptEvent | null = null;
  let installOutcome: string | null = null;

  // Listen for beforeinstallprompt event
  window.addEventListener('beforeinstallprompt', (e: Event) => {
    e.preventDefault();
    deferredPrompt = e as BeforeInstallPromptEvent;
    console.log('PWA install prompt available');
  });

  // Listen for appinstalled event
  window.addEventListener('appinstalled', () => {
    console.log('PWA was installed');
    deferredPrompt = null;
  });

  const promptInstall = async (): Promise<void> => {
    if (!deferredPrompt) {
      throw new Error('Install prompt not available');
    }

    try {
      await deferredPrompt.prompt();
      const choiceResult = await deferredPrompt.userChoice;
      installOutcome = choiceResult.outcome;
      console.log('PWA install prompt result:', choiceResult.outcome);
      
      if (choiceResult.outcome === 'accepted') {
        deferredPrompt = null;
      }
    } catch (error) {
      console.error('Error showing install prompt:', error);
      throw error;
    }
  };

  return {
    isInstallable: !!deferredPrompt,
    isInstalled: isAppInstalled(),
    prompt: deferredPrompt ? promptInstall : null,
    outcome: installOutcome,
  };
}

// Show update notification
function showUpdateNotification(): void {
  if (typeof window === 'undefined') return;

  // Create a simple notification
  const notification = document.createElement('div');
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #2563eb;
    color: white;
    padding: 16px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 10000;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    max-width: 300px;
  `;

  notification.innerHTML = `
    <div style="margin-bottom: 8px; font-weight: 600;">
      Update Available
    </div>
    <div style="margin-bottom: 12px; opacity: 0.9;">
      A new version of the app is available.
    </div>
    <div style="display: flex; gap: 8px;">
      <button id="update-btn" style="
        background: white;
        color: #2563eb;
        border: none;
        padding: 6px 12px;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        font-weight: 600;
      ">
        Update
      </button>
      <button id="dismiss-btn" style="
        background: transparent;
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
        padding: 6px 12px;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
      ">
        Later
      </button>
    </div>
  `;

  document.body.appendChild(notification);

  // Handle update button click
  const updateBtn = notification.querySelector('#update-btn');
  updateBtn?.addEventListener('click', () => {
    window.location.reload();
  });

  // Handle dismiss button click
  const dismissBtn = notification.querySelector('#dismiss-btn');
  dismissBtn?.addEventListener('click', () => {
    notification.remove();
  });

  // Auto-dismiss after 10 seconds
  setTimeout(() => {
    if (notification.parentNode) {
      notification.remove();
    }
  }, 10000);
}

// Cache management
export async function clearAppCache(): Promise<void> {
  if (typeof window === 'undefined' || !('caches' in window)) {
    return;
  }

  try {
    const cacheNames = await caches.keys();
    await Promise.all(
      cacheNames.map(cacheName => caches.delete(cacheName))
    );
    console.log('App cache cleared');
  } catch (error) {
    console.error('Error clearing cache:', error);
  }
}

// Get cache size
export async function getCacheSize(): Promise<number> {
  if (typeof window === 'undefined' || !('caches' in window)) {
    return 0;
  }

  try {
    const cacheNames = await caches.keys();
    let totalSize = 0;

    for (const cacheName of cacheNames) {
      const cache = await caches.open(cacheName);
      const requests = await cache.keys();
      
      for (const request of requests) {
        const response = await cache.match(request);
        if (response) {
          const blob = await response.blob();
          totalSize += blob.size;
        }
      }
    }

    return totalSize;
  } catch (error) {
    console.error('Error calculating cache size:', error);
    return 0;
  }
}

// Format cache size for display
export function formatCacheSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Network status monitoring
export function useNetworkStatus() {
  if (typeof window === 'undefined') {
    return { isOnline: true, isSlowConnection: false };
  }

  const isOnline = navigator.onLine;
  
  // Detect slow connection
  const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
  const isSlowConnection = connection ? 
    (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') : 
    false;

  return { isOnline, isSlowConnection };
}

// Background sync
export async function registerBackgroundSync(tag: string): Promise<void> {
  if (typeof window === 'undefined' || !('serviceWorker' in navigator)) {
    return;
  }

  try {
    const registration = await navigator.serviceWorker.ready;
    if ('sync' in registration) {
      await (registration as any).sync.register(tag);
      console.log('Background sync registered:', tag);
    }
  } catch (error) {
    console.error('Background sync registration failed:', error);
  }
}

// Push notifications
export async function requestNotificationPermission(): Promise<NotificationPermission> {
  if (typeof window === 'undefined' || !('Notification' in window)) {
    return 'denied';
  }

  if (Notification.permission === 'granted') {
    return 'granted';
  }

  if (Notification.permission === 'denied') {
    return 'denied';
  }

  const permission = await Notification.requestPermission();
  return permission;
}

// Show local notification
export function showNotification(title: string, options?: NotificationOptions): void {
  if (typeof window === 'undefined' || !('Notification' in window)) {
    return;
  }

  if (Notification.permission === 'granted') {
    new Notification(title, {
      icon: '/icons/icon-192x192.png',
      badge: '/icons/badge-72x72.png',
      ...options,
    });
  }
}

// PWA installation detection
export function detectPWAInstallation(): void {
  if (typeof window === 'undefined') return;

  // Check if running as PWA
  if (isAppInstalled()) {
    console.log('App is running as PWA');
    document.documentElement.classList.add('pwa-installed');
  }

  // Listen for display mode changes
  const mediaQuery = window.matchMedia('(display-mode: standalone)');
  mediaQuery.addEventListener('change', (e) => {
    if (e.matches) {
      console.log('App switched to standalone mode');
      document.documentElement.classList.add('pwa-installed');
    } else {
      console.log('App switched to browser mode');
      document.documentElement.classList.remove('pwa-installed');
    }
  });
}
