// frontend/src/lib/sitemap.ts
import { MetadataRoute } from 'next';
import { APP_CONFIG, PUBLIC_ROUTES } from '@/constants';

// Types for sitemap entries
interface SitemapEntry {
  url: string;
  lastModified?: string | Date;
  changeFrequency?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority?: number;
}

// Base URL for the application
const baseUrl = APP_CONFIG.URL;

// Static pages sitemap
export function generateStaticSitemap(): MetadataRoute.Sitemap {
  const staticPages: SitemapEntry[] = [
    {
      url: `${baseUrl}${PUBLIC_ROUTES.HOME}`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 1.0,
    },
    {
      url: `${baseUrl}${PUBLIC_ROUTES.MARKETPLACE}`,
      lastModified: new Date(),
      changeFrequency: 'hourly',
      priority: 0.9,
    },
    {
      url: `${baseUrl}${PUBLIC_ROUTES.EXPLORE}`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.8,
    },
    {
      url: `${baseUrl}${PUBLIC_ROUTES.POPULAR}`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.8,
    },
    {
      url: `${baseUrl}${PUBLIC_ROUTES.BLOG}`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}${PUBLIC_ROUTES.CATEGORIES}`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}${PUBLIC_ROUTES.ABOUT}`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.6,
    },
    {
      url: `${baseUrl}${PUBLIC_ROUTES.CONTACT}`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.6,
    },
    {
      url: `${baseUrl}${PUBLIC_ROUTES.CAREERS}`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.5,
    },
    {
      url: `${baseUrl}${PUBLIC_ROUTES.PRIVACY}`,
      lastModified: new Date(),
      changeFrequency: 'yearly',
      priority: 0.3,
    },
    {
      url: `${baseUrl}${PUBLIC_ROUTES.TERMS}`,
      lastModified: new Date(),
      changeFrequency: 'yearly',
      priority: 0.3,
    },
  ];

  return staticPages;
}

// Products sitemap generator
export async function generateProductsSitemap(): Promise<MetadataRoute.Sitemap> {
  try {
    // TODO: Replace with actual API call to fetch products
    // const products = await fetchAllProducts();
    
    // Mock data for now
    const products = [
      { id: '1', updatedAt: '2024-01-15T10:00:00Z' },
      { id: '2', updatedAt: '2024-01-14T15:30:00Z' },
      { id: '3', updatedAt: '2024-01-13T09:45:00Z' },
    ];

    return products.map((product) => ({
      url: `${baseUrl}/product/${product.id}`,
      lastModified: new Date(product.updatedAt),
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    }));
  } catch (error) {
    console.error('Error generating products sitemap:', error);
    return [];
  }
}

// Blog posts sitemap generator
export async function generatePostsSitemap(): Promise<MetadataRoute.Sitemap> {
  try {
    // TODO: Replace with actual API call to fetch posts
    // const posts = await fetchAllPosts();
    
    // Mock data for now
    const posts = [
      { id: '1', updatedAt: '2024-01-15T10:00:00Z' },
      { id: '2', updatedAt: '2024-01-14T15:30:00Z' },
      { id: '3', updatedAt: '2024-01-13T09:45:00Z' },
    ];

    return posts.map((post) => ({
      url: `${baseUrl}/post/${post.id}`,
      lastModified: new Date(post.updatedAt),
      changeFrequency: 'monthly' as const,
      priority: 0.6,
    }));
  } catch (error) {
    console.error('Error generating posts sitemap:', error);
    return [];
  }
}

// Categories sitemap generator
export async function generateCategoriesSitemap(): Promise<MetadataRoute.Sitemap> {
  try {
    // TODO: Replace with actual API call to fetch categories
    // const categories = await fetchAllCategories();
    
    // Mock data for now
    const categories = [
      { slug: 'electronics', updatedAt: '2024-01-10T10:00:00Z' },
      { slug: 'clothing', updatedAt: '2024-01-09T15:30:00Z' },
      { slug: 'home-garden', updatedAt: '2024-01-08T09:45:00Z' },
    ];

    return categories.map((category) => ({
      url: `${baseUrl}/categories/${category.slug}`,
      lastModified: new Date(category.updatedAt),
      changeFrequency: 'weekly' as const,
      priority: 0.7,
    }));
  } catch (error) {
    console.error('Error generating categories sitemap:', error);
    return [];
  }
}

// Main sitemap generator
export async function generateMainSitemap(): Promise<MetadataRoute.Sitemap> {
  const staticSitemap = generateStaticSitemap();
  const productsSitemap = await generateProductsSitemap();
  const postsSitemap = await generatePostsSitemap();
  const categoriesSitemap = await generateCategoriesSitemap();

  return [
    ...staticSitemap,
    ...productsSitemap.slice(0, 1000), // Limit to 1000 products in main sitemap
    ...postsSitemap.slice(0, 500), // Limit to 500 posts in main sitemap
    ...categoriesSitemap,
  ];
}

// Sitemap index generator (for large sites)
export function generateSitemapIndex(): MetadataRoute.Sitemap {
  return [
    {
      url: `${baseUrl}/sitemap.xml`,
      lastModified: new Date(),
    },
    {
      url: `${baseUrl}/sitemap-products.xml`,
      lastModified: new Date(),
    },
    {
      url: `${baseUrl}/sitemap-posts.xml`,
      lastModified: new Date(),
    },
    {
      url: `${baseUrl}/sitemap-categories.xml`,
      lastModified: new Date(),
    },
  ];
}

// Utility function to validate sitemap entries
export function validateSitemapEntry(entry: SitemapEntry): boolean {
  try {
    new URL(entry.url);
    return true;
  } catch {
    return false;
  }
}

// Utility function to format sitemap for XML
export function formatSitemapXML(sitemap: MetadataRoute.Sitemap): string {
  const xmlHeader = '<?xml version="1.0" encoding="UTF-8"?>';
  const urlsetOpen = '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">';
  const urlsetClose = '</urlset>';

  const urls = sitemap
    .filter(validateSitemapEntry)
    .map((entry) => {
      const url = `<url>
        <loc>${entry.url}</loc>
        ${entry.lastModified ? `<lastmod>${new Date(entry.lastModified).toISOString()}</lastmod>` : ''}
        ${entry.changeFrequency ? `<changefreq>${entry.changeFrequency}</changefreq>` : ''}
        ${entry.priority ? `<priority>${entry.priority}</priority>` : ''}
      </url>`;
      return url;
    })
    .join('\n');

  return `${xmlHeader}\n${urlsetOpen}\n${urls}\n${urlsetClose}`;
}

// Utility function to generate robots.txt content
export function generateRobotsTxt(): string {
  return `# Market O'Clock - Robots.txt

User-agent: *
Allow: /

# Disallow private areas
Disallow: /dashboard/
Disallow: /checkout/
Disallow: /order/
Disallow: /api/
Disallow: /admin/
Disallow: /_next/

# Sitemap location
Sitemap: ${baseUrl}/sitemap.xml
Sitemap: ${baseUrl}/sitemap-products.xml
Sitemap: ${baseUrl}/sitemap-posts.xml
Sitemap: ${baseUrl}/sitemap-categories.xml

# Crawl delay
Crawl-delay: 1`;
}
