// frontend/src/contexts/CartContext.tsx
'use client';

import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { useLocalStorage } from '@/hooks/useLocalStorage';
import type { Product, CartItem, Cart } from '@/types';
import { ORDER_CONFIG } from '@/constants';

// Cart Action Types
type CartAction =
  | { type: 'ADD_ITEM'; payload: { product: Product; quantity: number } }
  | { type: 'REMOVE_ITEM'; payload: { productId: string } }
  | { type: 'UPDATE_QUANTITY'; payload: { productId: string; quantity: number } }
  | { type: 'CLEAR_CART' }
  | { type: 'LOAD_CART'; payload: { items: CartItem[] } }
  | { type: 'SET_SHIPPING_METHOD'; payload: { method: keyof typeof ORDER_CONFIG.SHIPPING_RATES } };

// Cart State
interface CartState extends Cart {
  shippingMethod: keyof typeof ORDER_CONFIG.SHIPPING_RATES;
  isLoading: boolean;
  error: string | null;
}

// Cart Context Type
interface CartContextType extends CartState {
  addItem: (product: Product, quantity?: number) => void;
  removeItem: (productId: string) => void;
  updateQuantity: (productId: string, quantity: number) => void;
  clearCart: () => void;
  setShippingMethod: (method: keyof typeof ORDER_CONFIG.SHIPPING_RATES) => void;
  getItemQuantity: (productId: string) => number;
  isInCart: (productId: string) => boolean;
  itemCount: number;
}

// Initial State
const initialState: CartState = {
  id: 'local-cart',
  items: [],
  subtotal: 0,
  tax: 0,
  shipping: 0,
  total: 0,
  shippingMethod: 'standard',
  isLoading: false,
  error: null,
  updatedAt: new Date().toISOString(),
};

// Cart Reducer
const cartReducer = (state: CartState, action: CartAction): CartState => {
  switch (action.type) {
    case 'ADD_ITEM': {
      const { product, quantity } = action.payload;
      const existingItemIndex = state.items.findIndex(item => item.productId === product.id);
      
      let newItems: CartItem[];
      if (existingItemIndex >= 0) {
        newItems = state.items.map((item, index) =>
          index === existingItemIndex
            ? { ...item, quantity: item.quantity + quantity }
            : item
        );
      } else {
        const newItem: CartItem = {
          productId: product.id,
          product,
          quantity,
          addedAt: new Date().toISOString(),
        };
        newItems = [...state.items, newItem];
      }
      
      return calculateTotals({ ...state, items: newItems });
    }

    case 'REMOVE_ITEM': {
      const newItems = state.items.filter(item => item.productId !== action.payload.productId);
      return calculateTotals({ ...state, items: newItems });
    }

    case 'UPDATE_QUANTITY': {
      const { productId, quantity } = action.payload;
      if (quantity <= 0) {
        return cartReducer(state, { type: 'REMOVE_ITEM', payload: { productId } });
      }
      
      const newItems = state.items.map(item =>
        item.productId === productId ? { ...item, quantity } : item
      );
      return calculateTotals({ ...state, items: newItems });
    }

    case 'CLEAR_CART':
      return { ...initialState, shippingMethod: state.shippingMethod };

    case 'LOAD_CART':
      return calculateTotals({ ...state, items: action.payload.items });

    case 'SET_SHIPPING_METHOD':
      return calculateTotals({ ...state, shippingMethod: action.payload.method });

    default:
      return state;
  }
};

// Calculate totals helper
const calculateTotals = (state: CartState): CartState => {
  const subtotal = state.items.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);
  const tax = subtotal * ORDER_CONFIG.TAX_RATE;
  const shipping = state.items.length > 0 
    ? (subtotal >= ORDER_CONFIG.FREE_SHIPPING_THRESHOLD ? 0 : ORDER_CONFIG.SHIPPING_RATES[state.shippingMethod])
    : 0;
  const total = subtotal + tax + shipping;

  return {
    ...state,
    subtotal,
    tax,
    shipping,
    total,
    updatedAt: new Date().toISOString(),
  };
};

// Create Context
const CartContext = createContext<CartContextType | undefined>(undefined);

// Cart Provider Component
interface CartProviderProps {
  children: ReactNode;
}

export const CartProvider: React.FC<CartProviderProps> = ({ children }) => {
  const [cartItems, setCartItems] = useLocalStorage<CartItem[]>('cart-items', []);
  const [state, dispatch] = useReducer(cartReducer, initialState);

  // Load cart from localStorage on mount
  useEffect(() => {
    if (cartItems.length > 0) {
      dispatch({ type: 'LOAD_CART', payload: { items: cartItems } });
    }
  }, []);

  // Save cart to localStorage when items change
  useEffect(() => {
    setCartItems(state.items);
  }, [state.items, setCartItems]);

  // Context value
  const contextValue: CartContextType = {
    ...state,
    addItem: (product: Product, quantity = 1) => {
      dispatch({ type: 'ADD_ITEM', payload: { product, quantity } });
    },
    removeItem: (productId: string) => {
      dispatch({ type: 'REMOVE_ITEM', payload: { productId } });
    },
    updateQuantity: (productId: string, quantity: number) => {
      dispatch({ type: 'UPDATE_QUANTITY', payload: { productId, quantity } });
    },
    clearCart: () => {
      dispatch({ type: 'CLEAR_CART' });
    },
    setShippingMethod: (method: keyof typeof ORDER_CONFIG.SHIPPING_RATES) => {
      dispatch({ type: 'SET_SHIPPING_METHOD', payload: { method } });
    },
    getItemQuantity: (productId: string) => {
      const item = state.items.find(item => item.productId === productId);
      return item ? item.quantity : 0;
    },
    isInCart: (productId: string) => {
      return state.items.some(item => item.productId === productId);
    },
    itemCount: state.items.reduce((sum, item) => sum + item.quantity, 0),
  };

  return (
    <CartContext.Provider value={contextValue}>
      {children}
    </CartContext.Provider>
  );
};

// Custom hook to use cart context
export const useCartContext = (): CartContextType => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCartContext must be used within a CartProvider');
  }
  return context;
};
